"""
🚀 SIMPLE EXPORT SCRIPT
Export the already loaded data and model information
"""
import os
import numpy as np
import pandas as pd
import json
from datetime import datetime

def create_export_directory():
    """Create export directory with timestamp"""
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    export_dir = f"exported_results_{timestamp}"
    os.makedirs(export_dir, exist_ok=True)
    print(f"📁 Created export directory: {export_dir}")
    return export_dir

def load_data_simple():
    """Load data using the working quick_load approach"""
    from load_data import (
        load_mat_data, 
        prepare_input_data, 
        reshape_time_series_data_v8,
        prepare_train_test_data
    )
    
    print("📊 Loading data...")
    all_data = load_mat_data('data')
    input_data, output_labels = prepare_input_data(all_data)
    
    # Skip augmentation to avoid the error
    print("⚠️  Skipping augmentation to avoid errors...")
    
    # Use input_data directly for reshaping
    reshaped_data, reshaped_labels = reshape_time_series_data_v8(
        input_data, output_labels, 
        segments_per_new_sample=10, 
        segment_length=4000
    )
    
    X_train, X_valid, y_train, y_valid = prepare_train_test_data(
        reshaped_data, reshaped_labels, test_size=0.2, random_state=42
    )
    
    return {
        'raw_data': all_data,
        'input_data': input_data,
        'output_labels': output_labels,
        'X_train': X_train,
        'X_valid': X_valid,
        'y_train': y_train,
        'y_valid': y_valid
    }

def export_data_files(data, export_dir):
    """Export data in multiple formats"""
    print("📊 Exporting data files...")
    
    # Create data directory
    data_dir = os.path.join(export_dir, "data")
    os.makedirs(data_dir, exist_ok=True)
    
    # Export as NumPy arrays
    np.save(os.path.join(data_dir, "X_train.npy"), data['X_train'])
    np.save(os.path.join(data_dir, "X_valid.npy"), data['X_valid'])
    np.save(os.path.join(data_dir, "y_train.npy"), data['y_train'])
    np.save(os.path.join(data_dir, "y_valid.npy"), data['y_valid'])
    
    # Export as compressed archive
    np.savez_compressed(
        os.path.join(data_dir, "complete_dataset.npz"),
        X_train=data['X_train'],
        X_valid=data['X_valid'],
        y_train=data['y_train'],
        y_valid=data['y_valid']
    )
    
    # Export data info
    data_info = {
        'X_train_shape': list(data['X_train'].shape),
        'X_valid_shape': list(data['X_valid'].shape),
        'y_train_shape': list(data['y_train'].shape),
        'y_valid_shape': list(data['y_valid'].shape),
        'num_classes': int(len(np.unique(data['y_train']))),
        'data_type': str(data['X_train'].dtype),
        'label_range': [int(np.min(data['y_train'])), int(np.max(data['y_train']))],
        'statistics': {
            'mean': float(np.mean(data['X_train'])),
            'std': float(np.std(data['X_train'])),
            'min': float(np.min(data['X_train'])),
            'max': float(np.max(data['X_train']))
        }
    }
    
    with open(os.path.join(data_dir, "data_info.json"), 'w') as f:
        json.dump(data_info, f, indent=2)
    
    print(f"   ✅ Saved to {data_dir}/")
    return data_info

def export_model_info(export_dir):
    """Export model information"""
    print("🤖 Exporting model information...")
    
    models_dir = os.path.join(export_dir, "models")
    os.makedirs(models_dir, exist_ok=True)
    
    # Check available models
    model_files = [
        '1d_cnn_model.keras',
        'cnn_lstm_model.keras',
        'lstm_model.keras',
        'resnet_model.keras'
    ]
    
    available_models = {}
    for model_file in model_files:
        if os.path.exists(model_file):
            size_mb = os.path.getsize(model_file) / (1024*1024)
            available_models[model_file.replace('.keras', '')] = {
                'file': model_file,
                'size_mb': size_mb,
                'path': os.path.abspath(model_file)
            }
    
    # Save model info
    with open(os.path.join(models_dir, "model_info.json"), 'w') as f:
        json.dump(available_models, f, indent=2)
    
    # Create loading script
    loading_script = """# Model Loading Script
import tensorflow as tf
import numpy as np

print("Loading models...")
models = {}

"""
    
    for name, info in available_models.items():
        loading_script += f"""try:
    models['{name}'] = tf.keras.models.load_model('{info['file']}')
    print(f"SUCCESS {name}: {{models['{name}'].count_params():,}} parameters")
except Exception as e:
    print(f"FAILED to load {name}: {{e}}")

"""
    
    loading_script += """
print(f"Successfully loaded {len(models)} models")
"""
    
    with open(os.path.join(models_dir, "load_models.py"), 'w', encoding='utf-8') as f:
        f.write(loading_script)
    
    print(f"   ✅ Saved to {models_dir}/")
    return available_models

def export_csv_files(data, export_dir):
    """Export CSV files for easy viewing"""
    print("📋 Exporting CSV files...")
    
    csv_dir = os.path.join(export_dir, "csv")
    os.makedirs(csv_dir, exist_ok=True)
    
    # Export labels
    pd.DataFrame({
        'sample_id': range(len(data['y_train'])),
        'label': data['y_train']
    }).to_csv(os.path.join(csv_dir, "train_labels.csv"), index=False)
    
    pd.DataFrame({
        'sample_id': range(len(data['y_valid'])),
        'label': data['y_valid']
    }).to_csv(os.path.join(csv_dir, "valid_labels.csv"), index=False)
    
    # Export sample statistics
    stats_data = []
    for i in range(min(100, data['X_train'].shape[0])):  # Limit to first 100 samples
        stats_data.append({
            'sample_id': i,
            'mean': np.mean(data['X_train'][i]),
            'std': np.std(data['X_train'][i]),
            'min': np.min(data['X_train'][i]),
            'max': np.max(data['X_train'][i]),
            'label': data['y_train'][i]
        })
    
    pd.DataFrame(stats_data).to_csv(os.path.join(csv_dir, "sample_statistics.csv"), index=False)
    
    print(f"   ✅ Saved to {csv_dir}/")

def create_summary_report(data_info, available_models, export_dir):
    """Create summary report"""
    print("📄 Creating summary report...")
    
    timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    
    report = f"""# Export Summary Report
Generated: {timestamp}

## Dataset Information
- Training samples: {data_info['X_train_shape'][0]}
- Validation samples: {data_info['X_valid_shape'][0]}
- Input shape: {data_info['X_train_shape'][1:]}
- Number of classes: {data_info['num_classes']}
- Data type: {data_info['data_type']}
- Label range: {data_info['label_range'][0]} to {data_info['label_range'][1]}

## Data Statistics
- Mean: {data_info['statistics']['mean']:.6f}
- Std: {data_info['statistics']['std']:.6f}
- Min: {data_info['statistics']['min']:.6f}
- Max: {data_info['statistics']['max']:.6f}

## Available Models ({len(available_models)})
"""
    
    total_size = 0
    for name, info in available_models.items():
        report += f"- {name}: {info['size_mb']:.1f} MB\n"
        total_size += info['size_mb']
    
    report += f"\nTotal model size: {total_size:.1f} MB\n"
    
    report += """
## Files Exported
- data/X_train.npy - Training data
- data/X_valid.npy - Validation data  
- data/y_train.npy - Training labels
- data/y_valid.npy - Validation labels
- data/complete_dataset.npz - Complete compressed dataset
- data/data_info.json - Dataset information
- models/model_info.json - Model information
- models/load_models.py - Model loading script
- csv/train_labels.csv - Training labels
- csv/valid_labels.csv - Validation labels
- csv/sample_statistics.csv - Sample statistics

## Usage
```python
# Load data
import numpy as np
data = np.load('data/complete_dataset.npz')
X_train = data['X_train']
X_valid = data['X_valid']
y_train = data['y_train']
y_valid = data['y_valid']

# Load models
exec(open('models/load_models.py').read())
```
"""
    
    with open(os.path.join(export_dir, "README.md"), 'w', encoding='utf-8') as f:
        f.write(report)
    
    print(f"   ✅ Created README.md")

def main():
    """Main export function"""
    print("🚀 SIMPLE EXPORT")
    print("="*50)
    
    # Load data
    data = load_data_simple()
    
    # Create export directory
    export_dir = create_export_directory()
    
    # Export everything
    data_info = export_data_files(data, export_dir)
    available_models = export_model_info(export_dir)
    export_csv_files(data, export_dir)
    create_summary_report(data_info, available_models, export_dir)
    
    # Calculate total size
    total_size = 0
    for root, dirs, files in os.walk(export_dir):
        for file in files:
            total_size += os.path.getsize(os.path.join(root, file))
    
    print("\n" + "="*50)
    print("✅ EXPORT COMPLETE!")
    print("="*50)
    print(f"📁 Location: {os.path.abspath(export_dir)}")
    print(f"📊 Data: {data['X_train'].shape[0]} training + {data['X_valid'].shape[0]} validation")
    print(f"🤖 Models: {len(available_models)} available")
    print(f"💾 Total size: {total_size / (1024*1024):.1f} MB")
    print(f"📋 Check README.md for details")
    
    return export_dir

if __name__ == "__main__":
    export_directory = main()
    print(f"\n🎉 Export successful!")
    print(f"📂 Open: {export_directory}")
