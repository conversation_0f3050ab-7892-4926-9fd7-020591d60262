"""
🔮 FINAL PREDICTIONS WITH AUGMENTED AND RESHAPED DATA
This script uses the data processed through augmentation and your reshape function
"""
import tensorflow as tf
import numpy as np
import pandas as pd
from datetime import datetime
from sklearn.model_selection import train_test_split

print("🔮 MAKING PREDICTIONS WITH AUGMENTED AND RESHAPED DATA")
print("="*70)

# STEP 1: Load the augmented and reshaped data
print("📊 Loading augmented and reshaped data...")

try:
    # Load the processed data
    data = np.load('augmented_reshaped_data_20250728_092500.npz')
    reshaped_data = data['reshaped_data']      # Shape: (1232, 10, 4000)
    reshaped_labels = data['reshaped_labels']  # Shape: (1232,)
    
    print(f"   ✅ Loaded {len(reshaped_labels)} samples")
    print(f"   📊 Input shape per sample: {reshaped_data.shape[1:]}")
    print(f"   🏷️  Number of classes: {len(np.unique(reshaped_labels))}")
    print(f"   📈 Data range: {np.min(reshaped_data):.6f} to {np.max(reshaped_data):.6f}")
    print(f"   🎯 Label range: {np.min(reshaped_labels)} to {np.max(reshaped_labels)}")
    
except Exception as e:
    print(f"   ❌ Error loading data: {e}")
    print("   Run augment_and_reshape.py first to generate the data")
    exit()

# STEP 2: Prepare train/test split
print(f"\n🔄 Preparing train/test split...")

# Convert labels to proper format for classification
if np.max(reshaped_labels) > 10:
    # Map continuous labels to discrete classes
    unique_labels = np.unique(reshaped_labels)
    label_mapping = {old_label: new_label for new_label, old_label in enumerate(unique_labels)}
    mapped_labels = np.array([label_mapping[label] for label in reshaped_labels])
    print(f"   📝 Mapped {len(unique_labels)} unique labels to classes 0-{len(unique_labels)-1}")
else:
    mapped_labels = reshaped_labels.astype(int)

# Split the data
X_train, X_valid, y_train, y_valid = train_test_split(
    reshaped_data, mapped_labels, test_size=0.2, random_state=42
)

print(f"   ✅ Training data: {X_train.shape}")
print(f"   ✅ Validation data: {X_valid.shape}")
print(f"   ✅ Training labels: {y_train.shape}")
print(f"   ✅ Validation labels: {y_valid.shape}")
print(f"   📊 Class distribution: {np.bincount(y_train)}")

# STEP 3: Load all 4 models
print(f"\n🤖 Loading models...")
models = {}

model_files = {
    '1d_cnn_model': '1d_cnn_model.keras',
    'cnn_lstm_model': 'cnn_lstm_model.keras',
    'lstm_model': 'lstm_model.keras',
    'resnet_model': 'resnet_model.keras'
}

for name, file_path in model_files.items():
    try:
        models[name] = tf.keras.models.load_model(file_path)
        print(f"   ✅ {name}: {models[name].count_params():,} parameters")
        print(f"      Input: {models[name].input_shape}")
        print(f"      Output: {models[name].output_shape}")
    except Exception as e:
        print(f"   ❌ {name}: {e}")

print(f"\n📊 Successfully loaded {len(models)}/4 models")

if len(models) == 0:
    print("❌ ERROR: No models could be loaded!")
    exit()

# STEP 4: Make predictions with all models
print(f"\n🔮 Making predictions on {len(y_valid)} validation samples...")
predictions_results = {}

for model_name, model in models.items():
    print(f"\n   Predicting with {model_name}...")
    
    try:
        # Make predictions on validation data
        raw_predictions = model.predict(X_valid, verbose=0)
        
        # Convert to class predictions
        if len(raw_predictions.shape) > 1 and raw_predictions.shape[1] > 1:
            # Multi-class classification
            predicted_classes = np.argmax(raw_predictions, axis=1)
            prediction_probabilities = raw_predictions
            max_probs = np.max(raw_predictions, axis=1)
        else:
            # Single output - treat as regression then convert to classes
            if len(raw_predictions.shape) > 1:
                raw_predictions = raw_predictions.flatten()
            predicted_classes = np.round(raw_predictions).astype(int)
            predicted_classes = np.clip(predicted_classes, 0, len(np.unique(y_train))-1)
            prediction_probabilities = raw_predictions
            max_probs = np.abs(raw_predictions)
        
        # Calculate metrics
        accuracy = np.mean(predicted_classes == y_valid)
        avg_confidence = np.mean(max_probs)
        
        # Store results
        predictions_results[model_name] = {
            'predicted_classes': predicted_classes,
            'raw_predictions': raw_predictions,
            'probabilities': prediction_probabilities,
            'accuracy': accuracy,
            'avg_confidence': avg_confidence,
            'max_probs': max_probs
        }
        
        print(f"      ✅ Accuracy: {accuracy:.4f} ({accuracy*100:.2f}%)")
        print(f"      📊 Avg confidence: {avg_confidence:.4f}")
        print(f"      🎯 Sample predictions: {predicted_classes[:10]}")
        print(f"      🏷️  Actual labels:      {y_valid[:10]}")
        
    except Exception as e:
        print(f"      ❌ Error: {e}")

# STEP 5: Comprehensive analysis
print(f"\n📊 COMPREHENSIVE MODEL COMPARISON")
print("="*70)

# Create detailed comparison DataFrame
comparison_data = {
    'sample_id': range(len(y_valid)),
    'actual': y_valid
}

for model_name, results in predictions_results.items():
    if 'predicted_classes' in results:
        comparison_data[f'{model_name}_pred'] = results['predicted_classes']
        comparison_data[f'{model_name}_correct'] = (results['predicted_classes'] == y_valid).astype(int)
        comparison_data[f'{model_name}_confidence'] = results['max_probs']

comparison_df = pd.DataFrame(comparison_data)

# Print detailed accuracy summary
print("🎯 DETAILED ACCURACY SUMMARY:")
accuracies = []
for model_name, results in predictions_results.items():
    if 'accuracy' in results:
        acc = results['accuracy']
        conf = results.get('avg_confidence', 0)
        accuracies.append((model_name, acc, conf))
        print(f"   {model_name:15s}: {acc:.4f} ({acc*100:.2f}%) | Confidence: {conf:.4f}")

# Find best model
if accuracies:
    best_model = max(accuracies, key=lambda x: x[1])
    print(f"\n🏆 BEST MODEL: {best_model[0]}")
    print(f"   Accuracy: {best_model[1]:.4f} ({best_model[1]*100:.2f}%)")
    print(f"   Confidence: {best_model[2]:.4f}")

# Per-class analysis
print(f"\n📈 PER-CLASS ANALYSIS:")
for model_name, results in predictions_results.items():
    if 'predicted_classes' in results:
        print(f"\n{model_name}:")
        y_pred = results['predicted_classes']
        
        for class_id in np.unique(y_valid):
            mask = y_valid == class_id
            if np.sum(mask) > 0:
                class_acc = np.mean(y_pred[mask] == class_id)
                count = np.sum(mask)
                print(f"  Class {class_id}: {class_acc:.4f} ({count:2d} samples)")

# STEP 6: Save comprehensive results
print(f"\n💾 Saving comprehensive results...")

timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

# Save detailed predictions
detailed_file = f"reshaped_predictions_{timestamp}.csv"
comparison_df.to_csv(detailed_file, index=False)
print(f"   ✅ Detailed predictions: {detailed_file}")

# Save model performance summary
performance_file = f"reshaped_performance_{timestamp}.csv"
performance_data = []

for model_name, results in predictions_results.items():
    if 'accuracy' in results:
        performance_data.append({
            'model': model_name,
            'accuracy': results['accuracy'],
            'avg_confidence': results.get('avg_confidence', 0),
            'parameters': models[model_name].count_params() if model_name in models else 0
        })

performance_df = pd.DataFrame(performance_data)
performance_df.to_csv(performance_file, index=False)
print(f"   ✅ Model performance: {performance_file}")

# Save comprehensive report
report_file = f"reshaped_prediction_report_{timestamp}.txt"
with open(report_file, 'w') as f:
    f.write("RESHAPED DATA PREDICTION REPORT\n")
    f.write("="*50 + "\n")
    f.write(f"Generated: {datetime.now()}\n\n")
    
    f.write("DATASET INFO:\n")
    f.write(f"Total samples: {len(reshaped_labels)} (after augmentation and reshaping)\n")
    f.write(f"Training samples: {len(y_train)}\n")
    f.write(f"Validation samples: {len(y_valid)}\n")
    f.write(f"Number of classes: {len(np.unique(y_valid))}\n")
    f.write(f"Input shape: {X_valid.shape}\n")
    f.write(f"Data pipeline: 11 → 44 (augmentation) → 1232 (reshaping)\n\n")
    
    f.write("MODEL PERFORMANCE:\n")
    for model_name, acc, conf in accuracies:
        f.write(f"{model_name}: {acc:.4f} ({acc*100:.2f}%) | Confidence: {conf:.4f}\n")
    
    if accuracies:
        f.write(f"\nBEST MODEL: {best_model[0]} ({best_model[1]:.4f})\n")

print(f"   ✅ Comprehensive report: {report_file}")

print(f"\n🎉 PREDICTION COMPLETE WITH RESHAPED DATA!")
print("="*70)
print(f"📊 Processed {len(y_valid)} validation samples")
print(f"🔄 Data pipeline: 11 original → 44 augmented → 1232 reshaped → {len(y_valid)} validation")
print(f"🤖 Used {len(models)} models")
print(f"📄 Files created:")
print(f"   - {detailed_file}")
print(f"   - {performance_file}")
print(f"   - {report_file}")

if accuracies:
    print(f"\n🏆 FINAL RESULTS (WITH AUGMENTATION + RESHAPING):")
    for model_name, acc, conf in sorted(accuracies, key=lambda x: x[1], reverse=True):
        print(f"   {model_name:15s}: {acc:.4f} ({acc*100:.2f}%) | Confidence: {conf:.4f}")

print(f"\n📋 Available variables:")
print(f"   - models: Dictionary of loaded models")
print(f"   - predictions_results: All prediction results")
print(f"   - comparison_df: DataFrame with detailed comparisons")
print(f"   - X_train, X_valid, y_train, y_valid: Train/validation split")
print(f"   - reshaped_data, reshaped_labels: Complete reshaped dataset")
