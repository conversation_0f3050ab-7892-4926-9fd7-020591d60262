"""
🔄 AUGMENT AND RESHAPE DATA
This script applies augmentation first, then reshapes the data using your reshape function
"""
import numpy as np
import random
from load_data import load_mat_data, prepare_input_data

def augment_time_series_data_fixed(input_data, labels, num_augmentations=4):
    """Apply data augmentation with fixed shape consistency"""
    print("🔧 Applying data augmentation...")
    
    augmented_data = []
    augmented_labels = []

    num_samples, num_channels, sequence_length = input_data.shape
    print(f"Original data shape: {input_data.shape}")

    for i in range(num_samples):
        for aug_idx in range(num_augmentations):
            # Choose a random augmentation technique
            augmentation_type = random.choice(['noise', 'reverse', 'crop_pad'])

            if augmentation_type == 'noise':
                # Add random noise
                noise = np.random.normal(0, 0.001, input_data[i].shape)
                augmented_sample = input_data[i] + noise

            elif augmentation_type == 'reverse':
                # Reverse the sequence
                augmented_sample = np.flip(input_data[i], axis=-1)

            elif augmentation_type == 'crop_pad':
                # Fixed crop and pad - maintain exact sequence length
                crop_size = random.randint(1, min(100, sequence_length // 100))
                
                # Crop from the beginning and pad at the end
                cropped_sample = input_data[i][:, crop_size:]
                
                # Pad to maintain original length
                pad_width = ((0, 0), (0, crop_size))
                augmented_sample = np.pad(cropped_sample, pad_width, mode='constant', constant_values=0)
                
                # Ensure exact shape match
                augmented_sample = augmented_sample[:, :sequence_length]

            # Verify shape consistency
            if augmented_sample.shape != input_data[i].shape:
                print(f"⚠️  Shape mismatch! Using original sample instead.")
                augmented_sample = input_data[i].copy()

            augmented_data.append(augmented_sample)
            augmented_labels.append(labels[i])

    # Convert to numpy arrays
    augmented_data = np.array(augmented_data)
    augmented_labels = np.array(augmented_labels)
    
    print(f"✅ Augmented data shape: {augmented_data.shape}")
    print(f"✅ Augmented labels shape: {augmented_labels.shape}")
    print(f"✅ Augmentation factor: {augmented_data.shape[0] / input_data.shape[0]:.1f}x")
    
    return augmented_data, augmented_labels

def reshape_time_series_data_v8(input_data, label_data, segments_per_new_sample, segment_length):
    """
    Reshape time series data and corresponding labels into a specified shape.
    (Your original function from run.py)
    """
    print(f"🔄 Reshaping data...")
    print(f"Input shape: {input_data.shape}")
    print(f"Segments per sample: {segments_per_new_sample}")
    print(f"Segment length: {segment_length}")
    
    num_samples_original, num_channels, length_original = input_data.shape

    # Validate the feasibility of reshaping
    if length_original % segment_length != 0:
        print(f"⚠️  Warning: Segment length {segment_length} does not evenly divide original length {length_original}")
        print(f"   Truncating to {(length_original // segment_length) * segment_length}")
        input_data = input_data[:, :, :(length_original // segment_length) * segment_length]
        length_original = input_data.shape[2]

    total_segments_per_original_sample = (length_original // segment_length) * num_channels
    num_samples_new = (num_samples_original * total_segments_per_original_sample) // segments_per_new_sample

    # Validate if reshaping is possible
    if (num_samples_original * total_segments_per_original_sample) % segments_per_new_sample != 0:
        print(f"⚠️  Warning: Reshaping not perfectly divisible. Some data will be truncated.")
        num_samples_new = (num_samples_original * total_segments_per_original_sample) // segments_per_new_sample

    # Initialize reshaped data and labels
    new_shape = (num_samples_new, segments_per_new_sample, segment_length)
    reshaped_data = np.zeros(new_shape)
    reshaped_labels = np.zeros(num_samples_new)

    # Reshape the data and labels
    count = 0
    for i in range(num_samples_original):
        segment_count = 0
        for j in range(num_channels):
            for k in range(length_original // segment_length):
                if count >= num_samples_new:
                    break
                    
                start_idx = k * segment_length
                end_idx = start_idx + segment_length
                reshaped_data[count, segment_count % segments_per_new_sample, :] = input_data[i, j, start_idx:end_idx]
                
                if (segment_count + 1) % segments_per_new_sample == 0:
                    reshaped_labels[count] = label_data[i]
                    count += 1
                segment_count += 1
            
            if count >= num_samples_new:
                break
        
        if count >= num_samples_new:
            break

    print(f"✅ Reshaped data shape: {reshaped_data.shape}")
    print(f"✅ Reshaped labels shape: {reshaped_labels.shape}")

    return reshaped_data, reshaped_labels

def main():
    """Main function to load, augment, and reshape data"""
    print("🚀 AUGMENT AND RESHAPE PIPELINE")
    print("="*50)
    
    # Set random seeds for reproducibility
    random.seed(42)
    np.random.seed(42)
    
    # Step 1: Load original data
    print("\n1. Loading original data...")
    all_data = load_mat_data('data')
    input_data, output_labels = prepare_input_data(all_data)
    
    print(f"   Original data shape: {input_data.shape}")
    print(f"   Original labels shape: {output_labels.shape}")
    
    # Step 2: Apply augmentation
    print("\n2. Applying augmentation...")
    augmented_data, augmented_labels = augment_time_series_data_fixed(
        input_data, output_labels, num_augmentations=4
    )
    
    # Step 3: Reshape the augmented data
    print("\n3. Reshaping augmented data...")
    segments_per_new_sample = 10
    segment_length = 4000
    
    reshaped_data, reshaped_labels = reshape_time_series_data_v8(
        augmented_data, augmented_labels, segments_per_new_sample, segment_length
    )
    
    # Step 4: Summary
    print("\n" + "="*50)
    print("📊 PIPELINE SUMMARY")
    print("="*50)
    print(f"Original data:    {input_data.shape}")
    print(f"Augmented data:   {augmented_data.shape}")
    print(f"Reshaped data:    {reshaped_data.shape}")
    print(f"Final labels:     {reshaped_labels.shape}")
    
    print(f"\nTransformation pipeline:")
    print(f"  {input_data.shape[0]} samples → {augmented_data.shape[0]} samples (augmentation)")
    print(f"  {augmented_data.shape[0]} samples → {reshaped_data.shape[0]} samples (reshaping)")
    print(f"  Final: {reshaped_data.shape[0]} samples with shape {reshaped_data.shape[1:]}")
    
    # Step 5: Verify data integrity
    print(f"\n🔍 Data verification:")
    print(f"   Data type: {reshaped_data.dtype}")
    print(f"   Data range: {np.min(reshaped_data):.6f} to {np.max(reshaped_data):.6f}")
    print(f"   Labels range: {np.min(reshaped_labels)} to {np.max(reshaped_labels)}")
    print(f"   Unique labels: {len(np.unique(reshaped_labels))}")
    
    # Step 6: Save results
    print(f"\n💾 Saving results...")
    timestamp = "20250728_092500"  # Use consistent timestamp
    
    # Save as NPZ
    np.savez_compressed(
        f'augmented_reshaped_data_{timestamp}.npz',
        reshaped_data=reshaped_data,
        reshaped_labels=reshaped_labels,
        original_shape=input_data.shape,
        augmented_shape=augmented_data.shape,
        final_shape=reshaped_data.shape
    )
    
    print(f"   ✅ Saved: augmented_reshaped_data_{timestamp}.npz")
    
    # Create usage example
    usage_code = f'''# USAGE EXAMPLE
import numpy as np

# Load the processed data
data = np.load('augmented_reshaped_data_{timestamp}.npz')
reshaped_data = data['reshaped_data']      # Shape: {reshaped_data.shape}
reshaped_labels = data['reshaped_labels']  # Shape: {reshaped_labels.shape}

print(f"Loaded {{len(reshaped_labels)}} samples")
print(f"Input shape per sample: {{reshaped_data.shape[1:]}}")
print(f"Number of classes: {{len(np.unique(reshaped_labels))}}")

# Ready for train/test split and model training
from sklearn.model_selection import train_test_split

X_train, X_valid, y_train, y_valid = train_test_split(
    reshaped_data, reshaped_labels, test_size=0.2, random_state=42
)

print(f"Training: {{X_train.shape}}")
print(f"Validation: {{X_valid.shape}}")
'''
    
    with open(f'usage_example_{timestamp}.py', 'w') as f:
        f.write(usage_code)
    
    print(f"   ✅ Saved: usage_example_{timestamp}.py")
    
    return {
        'original_data': input_data,
        'original_labels': output_labels,
        'augmented_data': augmented_data,
        'augmented_labels': augmented_labels,
        'reshaped_data': reshaped_data,
        'reshaped_labels': reshaped_labels
    }

if __name__ == "__main__":
    result = main()
    
    if result:
        print(f"\n✅ SUCCESS! Data pipeline completed:")
        print(f"   - Original: {result['original_data'].shape}")
        print(f"   - Augmented: {result['augmented_data'].shape}")
        print(f"   - Reshaped: {result['reshaped_data'].shape}")
        print(f"\n🎯 Ready for model training and prediction!")
        
        # Make variables available
        reshaped_data = result['reshaped_data']
        reshaped_labels = result['reshaped_labels']
        
        print(f"\nAvailable variables:")
        print(f"   - reshaped_data: {reshaped_data.shape}")
        print(f"   - reshaped_labels: {reshaped_labels.shape}")
