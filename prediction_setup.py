"""
🔮 PREDICTION SETUP - Data Ready for Model Predictions
This script prepares data and provides code for making predictions with all 4 models
"""
import os
import numpy as np
import pandas as pd
from datetime import datetime

def load_prediction_data():
    """Load data for predictions"""
    print("📊 Loading data for predictions...")
    
    # Try different data loading methods
    data = None
    
    # Method 1: From exported NPZ file
    if os.path.exists('exported_results_20250728_085759/data/complete_dataset.npz'):
        print("   Loading from exported NPZ file...")
        npz_data = np.load('exported_results_20250728_085759/data/complete_dataset.npz')
        data = {
            'X_train': npz_data['X_train'],
            'X_valid': npz_data['X_valid'],
            'y_train': npz_data['y_train'],
            'y_valid': npz_data['y_valid']
        }
    
    # Method 2: From individual NPY files
    elif os.path.exists('exported_results_20250728_085759/data/X_valid.npy'):
        print("   Loading from individual NPY files...")
        data = {
            'X_train': np.load('exported_results_20250728_085759/data/X_train.npy'),
            'X_valid': np.load('exported_results_20250728_085759/data/X_valid.npy'),
            'y_train': np.load('exported_results_20250728_085759/data/y_train.npy'),
            'y_valid': np.load('exported_results_20250728_085759/data/y_valid.npy')
        }
    
    # Method 3: Generate fresh data
    else:
        print("   Generating fresh data...")
        from simple_export import load_data_simple
        data = load_data_simple()
    
    if data:
        print("   ✅ Data loaded successfully!")
        print(f"   Training data: {data['X_train'].shape}")
        print(f"   Validation data: {data['X_valid'].shape}")
        print(f"   Training labels: {data['y_train'].shape}")
        print(f"   Validation labels: {data['y_valid'].shape}")
        print(f"   Number of classes: {len(np.unique(data['y_train']))}")
        print(f"   Label range: {np.min(data['y_train'])} to {np.max(data['y_train'])}")
    
    return data

def create_prediction_template():
    """Create template code for making predictions"""
    print("\n🔮 Creating prediction template...")
    
    template_code = '''"""
PREDICTION CODE TEMPLATE
Copy and paste this code in a TensorFlow-compatible environment
"""
import tensorflow as tf
import numpy as np
import pandas as pd
from datetime import datetime

# STEP 1: Load the data (already prepared)
print("Loading data...")
# Use the data variable from prediction_setup.py
X_train = data['X_train']
X_valid = data['X_valid'] 
y_train = data['y_train']
y_valid = data['y_valid']

print(f"Data shapes:")
print(f"  X_train: {X_train.shape}")
print(f"  X_valid: {X_valid.shape}")
print(f"  y_train: {y_train.shape}")
print(f"  y_valid: {y_valid.shape}")

# STEP 2: Load all 4 models
print("\\nLoading models...")
models = {}

try:
    models['1d_cnn_model'] = tf.keras.models.load_model('1d_cnn_model.keras')
    print("✅ 1D CNN model loaded")
except Exception as e:
    print(f"❌ 1D CNN model failed: {e}")

try:
    models['cnn_lstm_model'] = tf.keras.models.load_model('cnn_lstm_model.keras')
    print("✅ CNN-LSTM model loaded")
except Exception as e:
    print(f"❌ CNN-LSTM model failed: {e}")

try:
    models['lstm_model'] = tf.keras.models.load_model('lstm_model.keras')
    print("✅ LSTM model loaded")
except Exception as e:
    print(f"❌ LSTM model failed: {e}")

try:
    models['resnet_model'] = tf.keras.models.load_model('resnet_model.keras')
    print("✅ ResNet model loaded")
except Exception as e:
    print(f"❌ ResNet model failed: {e}")

print(f"\\nSuccessfully loaded {len(models)} models")

# STEP 3: Make predictions with all models
print("\\n🔮 Making predictions...")
predictions_results = {}

for model_name, model in models.items():
    print(f"\\nPredicting with {model_name}...")
    
    try:
        # Make predictions on validation data
        raw_predictions = model.predict(X_valid, verbose=0)
        
        # Convert to class predictions
        if len(raw_predictions.shape) > 1 and raw_predictions.shape[1] > 1:
            # Multi-class classification
            predicted_classes = np.argmax(raw_predictions, axis=1)
            prediction_probabilities = raw_predictions
        else:
            # Single output - treat as regression then convert to classes
            if len(raw_predictions.shape) > 1:
                raw_predictions = raw_predictions.flatten()
            predicted_classes = np.round(raw_predictions).astype(int)
            prediction_probabilities = raw_predictions
        
        # Calculate accuracy
        accuracy = np.mean(predicted_classes == y_valid)
        
        # Store results
        predictions_results[model_name] = {
            'predicted_classes': predicted_classes,
            'raw_predictions': raw_predictions,
            'probabilities': prediction_probabilities,
            'accuracy': accuracy
        }
        
        print(f"✅ {model_name}:")
        print(f"   Accuracy: {accuracy:.4f} ({accuracy*100:.2f}%)")
        print(f"   Predictions shape: {raw_predictions.shape}")
        print(f"   Sample predictions: {predicted_classes[:10]}")
        print(f"   Actual labels:      {y_valid[:10]}")
        
    except Exception as e:
        print(f"❌ Error with {model_name}: {e}")

# STEP 4: Compare all model performances
print("\\n📊 MODEL COMPARISON")
print("="*60)

# Create comparison DataFrame
comparison_data = {
    'sample_id': range(len(y_valid)),
    'actual': y_valid
}

for model_name, results in predictions_results.items():
    if 'predicted_classes' in results:
        comparison_data[f'{model_name}_pred'] = results['predicted_classes']

comparison_df = pd.DataFrame(comparison_data)

# Print accuracy summary
print("🎯 ACCURACY SUMMARY:")
accuracies = []
for model_name, results in predictions_results.items():
    if 'accuracy' in results:
        acc = results['accuracy']
        accuracies.append((model_name, acc))
        print(f"   {model_name:15s}: {acc:.4f} ({acc*100:.2f}%)")

# Find best model
if accuracies:
    best_model = max(accuracies, key=lambda x: x[1])
    print(f"\\n🏆 BEST MODEL: {best_model[0]} with {best_model[1]:.4f} accuracy")

# STEP 5: Detailed analysis
print("\\n📈 DETAILED ANALYSIS:")

# Class distribution
print("\\nClass distribution:")
print(f"Actual:     {np.bincount(y_valid)}")
for model_name, results in predictions_results.items():
    if 'predicted_classes' in results:
        pred_dist = np.bincount(results['predicted_classes'], minlength=len(np.bincount(y_valid)))
        print(f"{model_name:12s}: {pred_dist}")

# Per-class accuracy
print("\\nPer-class accuracy:")
for model_name, results in predictions_results.items():
    if 'predicted_classes' in results:
        print(f"\\n{model_name}:")
        y_pred = results['predicted_classes']
        for class_id in np.unique(y_valid):
            mask = y_valid == class_id
            if np.sum(mask) > 0:
                class_acc = np.mean(y_pred[mask] == class_id)
                count = np.sum(mask)
                print(f"  Class {class_id}: {class_acc:.4f} ({count} samples)")

# STEP 6: Save results
print("\\n💾 Saving results...")

# Save predictions to CSV
timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
csv_file = f"model_predictions_{timestamp}.csv"
comparison_df.to_csv(csv_file, index=False)
print(f"✅ Predictions saved to: {csv_file}")

# Save summary report
report_file = f"prediction_summary_{timestamp}.txt"
with open(report_file, 'w') as f:
    f.write("MODEL PREDICTION SUMMARY\\n")
    f.write("="*50 + "\\n")
    f.write(f"Generated: {datetime.now()}\\n\\n")
    
    f.write("DATASET INFO:\\n")
    f.write(f"Validation samples: {len(y_valid)}\\n")
    f.write(f"Number of classes: {len(np.unique(y_valid))}\\n")
    f.write(f"Input shape: {X_valid.shape}\\n\\n")
    
    f.write("MODEL ACCURACIES:\\n")
    for model_name, acc in accuracies:
        f.write(f"{model_name}: {acc:.4f} ({acc*100:.2f}%)\\n")
    
    if accuracies:
        f.write(f"\\nBEST MODEL: {best_model[0]} ({best_model[1]:.4f})\\n")

print(f"✅ Summary saved to: {report_file}")

print("\\n🎉 PREDICTION COMPLETE!")
print("Available variables:")
print("  - models: Dictionary of loaded models")
print("  - predictions_results: All prediction results")
print("  - comparison_df: DataFrame with all predictions")
'''
    
    # Save template to file
    with open('prediction_template.py', 'w') as f:
        f.write(template_code)
    
    print("   ✅ Template saved to: prediction_template.py")
    return template_code

def create_manual_prediction_guide(data):
    """Create step-by-step manual prediction guide"""
    print("\n📖 Creating manual prediction guide...")
    
    guide = f"""
# 🔮 MANUAL PREDICTION GUIDE

## Data Ready for Predictions
- Training samples: {data['X_train'].shape[0]}
- Validation samples: {data['X_valid'].shape[0]}
- Input shape: {data['X_valid'].shape[1:]}
- Number of classes: {len(np.unique(data['y_train']))}
- Label range: {np.min(data['y_train'])} to {np.max(data['y_train'])}

## Step-by-Step Instructions

### 1. Environment Setup
```bash
# Create TensorFlow environment
conda create -n tf_env python=3.9
conda activate tf_env
pip install tensorflow==2.13.0 numpy pandas matplotlib
```

### 2. Load Data (Copy this code)
```python
import numpy as np

# Load from exported files
data = np.load('exported_results_20250728_085759/data/complete_dataset.npz')
X_train = data['X_train']  # Shape: {data['X_train'].shape}
X_valid = data['X_valid']  # Shape: {data['X_valid'].shape}
y_train = data['y_train']  # Shape: {data['y_train'].shape}
y_valid = data['y_valid']  # Shape: {data['y_valid'].shape}

print(f"Data loaded - Validation samples: {{len(y_valid)}}")
```

### 3. Load Models (Copy this code)
```python
import tensorflow as tf

models = {{}}
model_files = [
    '1d_cnn_model.keras',
    'cnn_lstm_model.keras', 
    'lstm_model.keras',
    'resnet_model.keras'
]

for model_file in model_files:
    try:
        name = model_file.replace('.keras', '')
        models[name] = tf.keras.models.load_model(model_file)
        print(f"✅ {{name}}: {{models[name].count_params():,}} parameters")
    except Exception as e:
        print(f"❌ Failed to load {{model_file}}: {{e}}")

print(f"Loaded {{len(models)}} models")
```

### 4. Make Predictions (Copy this code)
```python
import pandas as pd

predictions = {{}}

for name, model in models.items():
    print(f"\\nPredicting with {{name}}...")
    
    # Make predictions
    raw_pred = model.predict(X_valid, verbose=0)
    
    # Convert to classes
    if len(raw_pred.shape) > 1 and raw_pred.shape[1] > 1:
        pred_classes = np.argmax(raw_pred, axis=1)
    else:
        pred_classes = np.round(raw_pred.flatten()).astype(int)
    
    # Calculate accuracy
    accuracy = np.mean(pred_classes == y_valid)
    
    predictions[name] = {{
        'classes': pred_classes,
        'raw': raw_pred,
        'accuracy': accuracy
    }}
    
    print(f"Accuracy: {{accuracy:.4f}} ({{accuracy*100:.2f}}%)")

# Find best model
best = max(predictions.items(), key=lambda x: x[1]['accuracy'])
print(f"\\n🏆 Best model: {{best[0]}} ({{best[1]['accuracy']:.4f}} accuracy)")
```

### 5. Analyze Results
```python
# Create comparison DataFrame
results_df = pd.DataFrame({{
    'actual': y_valid,
    **{{f'{{name}}_pred': pred['classes'] for name, pred in predictions.items()}}
}})

# Show sample results
print("\\nSample predictions (first 10):")
print(results_df.head(10))

# Save results
results_df.to_csv('all_model_predictions.csv', index=False)
print("\\nResults saved to: all_model_predictions.csv")
```

## Expected Results
Based on the data characteristics:
- Input shape: {data['X_valid'].shape[1:]} (10 channels × 4000 time points)
- {len(np.unique(data['y_train']))} classes to predict
- {data['X_valid'].shape[0]} validation samples to classify

## Model Information
1. **1D CNN Model**: ~23.5 MB - Good for spatial patterns
2. **CNN-LSTM Model**: ~24.3 MB - Combines spatial and temporal features  
3. **LSTM Model**: ~12.3 MB - Focuses on temporal sequences
4. **ResNet Model**: ~36.2 MB - Deep residual learning

## Files Available
- `prediction_template.py`: Complete runnable code
- `manual_prediction_guide.md`: This guide
- `exported_results_20250728_085759/`: All data files
"""
    
    with open('manual_prediction_guide.md', 'w', encoding='utf-8') as f:
        f.write(guide)
    
    print("   ✅ Guide saved to: manual_prediction_guide.md")

def main():
    """Main function"""
    print("🔮 PREDICTION SETUP")
    print("="*50)
    
    # Load data
    data = load_prediction_data()
    if not data:
        print("❌ Failed to load data")
        return None
    
    # Create prediction template
    template_code = create_prediction_template()
    
    # Create manual guide
    create_manual_prediction_guide(data)
    
    print("\n" + "="*50)
    print("✅ PREDICTION SETUP COMPLETE!")
    print("="*50)
    print(f"📊 Data ready: {data['X_valid'].shape[0]} validation samples")
    print(f"🎯 Classes to predict: {len(np.unique(data['y_train']))}")
    print(f"📝 Template created: prediction_template.py")
    print(f"📖 Guide created: manual_prediction_guide.md")
    
    print(f"\n🚀 NEXT STEPS:")
    print(f"1. Set up TensorFlow environment")
    print(f"2. Run: python prediction_template.py")
    print(f"3. Or follow manual_prediction_guide.md")
    
    return data

if __name__ == "__main__":
    data = main()
    
    if data:
        print(f"\n📋 Data available in 'data' variable:")
        print(f"   data['X_train'].shape = {data['X_train'].shape}")
        print(f"   data['X_valid'].shape = {data['X_valid'].shape}")
        print(f"   data['y_train'].shape = {data['y_train'].shape}")
        print(f"   data['y_valid'].shape = {data['y_valid'].shape}")
        
        print(f"\n💡 To make predictions:")
        print(f"   1. Copy prediction_template.py to TensorFlow environment")
        print(f"   2. Run the template with your 4 models")
        print(f"   3. Get classification results for all models")
