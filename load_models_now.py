"""
Load models with current environment - attempting different strategies
"""
import os
import sys

def try_load_models():
    """Try to load models with different strategies"""
    print("🔄 Attempting to load Keras models...")
    
    # Strategy 1: Try importing TensorFlow with error handling
    try:
        print("Attempting TensorFlow import...")
        import tensorflow as tf
        print("✅ TensorFlow imported successfully!")
        
        # Try to load models
        model_files = [
            '1d_cnn_model.keras',
            'cnn_lstm_model.keras', 
            'lstm_model.keras',
            'resnet_model.keras'
        ]
        
        models = {}
        
        for model_file in model_files:
            if os.path.exists(model_file):
                model_name = model_file.replace('.keras', '')
                print(f"\n🔄 Loading {model_name}...")
                
                try:
                    # Try loading with compile=False first
                    model = tf.keras.models.load_model(model_file, compile=False)
                    models[model_name] = model
                    print(f"✅ Successfully loaded {model_name}")
                    print(f"   Input shape: {model.input_shape}")
                    print(f"   Output shape: {model.output_shape}")
                    print(f"   Parameters: {model.count_params():,}")
                    
                except Exception as e:
                    print(f"❌ Failed to load {model_name}: {str(e)}")
        
        return models
        
    except Exception as e:
        print(f"❌ TensorFlow import/loading failed: {str(e)}")
        return None

def load_data_simple():
    """Load the processed data"""
    try:
        from complete_loader_summary import load_complete_dataset
        print("🔄 Loading processed data...")
        data = load_complete_dataset()
        if data:
            print("✅ Data loaded successfully!")
            return data
        else:
            print("❌ Data loading failed")
            return None
    except Exception as e:
        print(f"❌ Error loading data: {str(e)}")
        return None

def main():
    """Main loading function"""
    print("🚀 LOADING MODELS AND DATA")
    print("="*50)
    
    # Load data first (this should work)
    data = load_data_simple()
    
    # Try to load models
    models = try_load_models()
    
    # Summary
    print("\n" + "="*50)
    print("📋 LOADING RESULTS")
    print("="*50)
    
    if data:
        print("✅ Data loaded successfully")
        print(f"   Training samples: {data['X_train'].shape[0]}")
        print(f"   Validation samples: {data['X_valid'].shape[0]}")
        print(f"   Input shape: {data['X_train'].shape[1:]}")
        print(f"   Number of classes: {len(set(data['y_train']))}")
    else:
        print("❌ Data loading failed")
    
    if models:
        print(f"✅ Models loaded: {len(models)}")
        for name in models.keys():
            print(f"   - {name}")
    else:
        print("❌ Model loading failed")
        print("💡 Try running in a fresh conda environment:")
        print("   conda create -n tf_env python=3.9")
        print("   conda activate tf_env") 
        print("   pip install tensorflow==2.13.0 numpy==1.24.3")
    
    return {
        'models': models,
        'data': data
    }

if __name__ == "__main__":
    result = main()
    
    # Make variables available
    models = result['models']
    data = result['data']
    
    print("\n" + "="*50)
    print("✅ VARIABLES AVAILABLE")
    print("="*50)
    print("- models: Dictionary of loaded Keras models (if successful)")
    print("- data: Complete processed dataset")
    
    if data:
        print("\n📊 Data structure:")
        for key in data.keys():
            if hasattr(data[key], 'shape'):
                print(f"   data['{key}']: {data[key].shape}")
            else:
                print(f"   data['{key}']: {type(data[key])}")
    
    if models:
        print("\n🤖 Available models:")
        for name, model in models.items():
            print(f"   models['{name}']")
        
        print("\n📝 Example usage:")
        model_name = list(models.keys())[0]
        print(f"   models['{model_name}'].summary()")
        print(f"   predictions = models['{model_name}'].predict(data['X_valid'])")
