"""
🔮 PREDICTION READY - Data Loaded and Instructions Provided
"""
import os
import numpy as np

def load_data_for_predictions():
    """Load data for predictions"""
    print("📊 Loading data for predictions...")
    
    # Try different loading methods
    data = None
    
    # Method 1: From exported files
    if os.path.exists('exported_results_20250728_085759/data/complete_dataset.npz'):
        print("   Loading from exported NPZ...")
        npz_data = np.load('exported_results_20250728_085759/data/complete_dataset.npz')
        data = {
            'X_train': npz_data['X_train'],
            'X_valid': npz_data['X_valid'],
            'y_train': npz_data['y_train'],
            'y_valid': npz_data['y_valid']
        }
    else:
        # Method 2: Generate fresh data
        print("   Generating fresh data...")
        from simple_export import load_data_simple
        data = load_data_simple()
    
    if data:
        print("   SUCCESS: Data loaded!")
        print(f"   Training: {data['X_train'].shape}")
        print(f"   Validation: {data['X_valid'].shape}")
        print(f"   Classes: {len(np.unique(data['y_train']))}")
    
    return data

def main():
    """Main function"""
    print("🔮 PREDICTION READY")
    print("="*50)
    
    # Load data
    data = load_data_for_predictions()
    
    if not data:
        print("ERROR: Could not load data")
        return None
    
    print("\n✅ DATA READY FOR PREDICTIONS!")
    print("="*50)
    print(f"📊 Validation samples: {data['X_valid'].shape[0]}")
    print(f"🎯 Classes to predict: {len(np.unique(data['y_train']))}")
    print(f"📐 Input shape: {data['X_valid'].shape[1:]}")
    print(f"🏷️  Label range: {np.min(data['y_train'])} to {np.max(data['y_train'])}")
    
    print(f"\n🤖 MODELS AVAILABLE:")
    model_files = ['1d_cnn_model.keras', 'cnn_lstm_model.keras', 'lstm_model.keras', 'resnet_model.keras']
    for i, model_file in enumerate(model_files, 1):
        if os.path.exists(model_file):
            size_mb = os.path.getsize(model_file) / (1024*1024)
            print(f"   {i}. {model_file.replace('.keras', '')}: {size_mb:.1f} MB")
        else:
            print(f"   {i}. {model_file}: NOT FOUND")
    
    print(f"\n🚀 TO MAKE PREDICTIONS:")
    print(f"1. Copy PREDICTION_CODE.py to TensorFlow environment")
    print(f"2. Run: python PREDICTION_CODE.py")
    print(f"3. Get classification results for all 4 models")
    
    print(f"\n📋 EXPECTED OUTPUT:")
    print(f"   - Model accuracy comparison")
    print(f"   - Predictions for {data['X_valid'].shape[0]} samples")
    print(f"   - Per-class accuracy analysis")
    print(f"   - CSV files with all results")
    print(f"   - Best performing model identification")
    
    return data

if __name__ == "__main__":
    data = main()
    
    if data:
        print(f"\n🎉 READY TO PREDICT!")
        print(f"Data loaded: {data['X_valid'].shape[0]} samples to classify")
        print(f"Models available: 4 Keras models")
        print(f"Prediction code: PREDICTION_CODE.py")
        
        # Show sample data
        print(f"\nSample validation data:")
        print(f"  First 5 labels: {data['y_valid'][:5]}")
        print(f"  Data range: {np.min(data['X_valid']):.6f} to {np.max(data['X_valid']):.6f}")
        print(f"  Data mean: {np.mean(data['X_valid']):.6f}")
        print(f"  Data std: {np.std(data['X_valid']):.6f}")
        
        print(f"\n💡 Next step: Run PREDICTION_CODE.py in TensorFlow environment!")
