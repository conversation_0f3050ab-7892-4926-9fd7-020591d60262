"""
FINAL MODEL AND DATA LOADER
Use this script in a compatible TensorFlow environment
"""
import tensorflow as tf
import numpy as np
import pickle
from complete_loader_summary import load_complete_dataset

def load_all_models_and_data():
    """Load all 4 models and prepared data"""
    print("🚀 Loading All Models and Data")
    print("="*50)
    
    # Load the prepared data
    print("📊 Loading prepared data...")
    data = load_complete_dataset()
    
    if not data:
        print("❌ Failed to load data")
        return None
    
    # Load all 4 models
    print("\n🤖 Loading Keras models...")
    models = {}
    model_files = {
        '1d_cnn_model': '1d_cnn_model.keras',
        'cnn_lstm_model': 'cnn_lstm_model.keras', 
        'lstm_model': 'lstm_model.keras',
        'resnet_model': 'resnet_model.keras'
    }
    
    for model_name, model_file in model_files.items():
        try:
            models[model_name] = tf.keras.models.load_model(model_file)
            print(f"✅ {model_name}: {models[model_name].count_params():,} parameters")
        except Exception as e:
            print(f"❌ Failed to load {model_name}: {str(e)}")
    
    # Try to load results
    print("\n📈 Loading results...")
    results = None
    try:
        with open('model_comparison_results_20250628_001945.pkl', 'rb') as f:
            results = pickle.load(f)
        print("✅ Results loaded successfully")
    except Exception as e:
        print(f"⚠️  Results loading failed: {str(e)}")
    
    return {
        'models': models,
        'data': data,
        'results': results
    }

def evaluate_all_models(models, data):
    """Evaluate all models on validation data"""
    print("\n🔍 Evaluating all models...")
    
    X_valid = data['X_valid']
    y_valid = data['y_valid']
    
    evaluation_results = {}
    
    for model_name, model in models.items():
        print(f"\nEvaluating {model_name}...")
        try:
            # Evaluate model
            loss, accuracy = model.evaluate(X_valid, y_valid, verbose=0)
            
            # Make predictions
            predictions = model.predict(X_valid, verbose=0)
            predicted_classes = np.argmax(predictions, axis=1)
            
            evaluation_results[model_name] = {
                'loss': loss,
                'accuracy': accuracy,
                'predictions': predictions,
                'predicted_classes': predicted_classes
            }
            
            print(f"   Loss: {loss:.4f}")
            print(f"   Accuracy: {accuracy:.4f}")
            
        except Exception as e:
            print(f"   ❌ Error: {str(e)}")
            evaluation_results[model_name] = {'error': str(e)}
    
    return evaluation_results

def main():
    """Main execution function"""
    # Load everything
    loaded = load_all_models_and_data()
    
    if not loaded:
        print("❌ Failed to load models and data")
        return None
    
    models = loaded['models']
    data = loaded['data']
    results = loaded['results']
    
    # Evaluate models if loaded successfully
    evaluation = None
    if models and data:
        evaluation = evaluate_all_models(models, data)
    
    # Summary
    print("\n" + "="*50)
    print("📋 FINAL SUMMARY")
    print("="*50)
    print(f"Models loaded: {len(models)}")
    print(f"Data loaded: {'Yes' if data else 'No'}")
    print(f"Results loaded: {'Yes' if results else 'No'}")
    print(f"Evaluation completed: {'Yes' if evaluation else 'No'}")
    
    if evaluation:
        print("\n🏆 Model Performance:")
        for model_name, result in evaluation.items():
            if 'accuracy' in result:
                print(f"   {model_name}: {result['accuracy']:.4f} accuracy")
    
    return {
        'models': models,
        'data': data,
        'results': results,
        'evaluation': evaluation
    }

if __name__ == "__main__":
    # Execute main function
    final_result = main()
    
    # Make variables available globally
    if final_result:
        models = final_result['models']
        data = final_result['data']
        results = final_result['results']
        evaluation = final_result['evaluation']
        
        print("\n✅ All variables are now available:")
        print("   - models: Dictionary of 4 loaded Keras models")
        print("   - data: Complete processed dataset")
        print("   - results: Model comparison results (if loaded)")
        print("   - evaluation: Model evaluation results")
        
        print("\n📝 Example usage:")
        if models:
            model_name = list(models.keys())[0]
            print(f"   models['{model_name}'].summary()")
            print(f"   predictions = models['{model_name}'].predict(data['X_valid'])")
            print(f"   models['{model_name}'].evaluate(data['X_valid'], data['y_valid'])")
