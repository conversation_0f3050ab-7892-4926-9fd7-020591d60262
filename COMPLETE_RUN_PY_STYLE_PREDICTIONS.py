"""
🔥 COMPLETE PREDICTIONS EXACTLY LIKE RUN.PY 🔥
This script replicates the exact structure and export format of run.py
for making predictions with the 4 models on reshaped data.

Copy this entire file to a TensorFlow environment and run it.
"""

# ============================================================================
# IMPORTS - EXACTLY LIKE RUN.PY
# ============================================================================

# Core libraries
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from copy import deepcopy
import time
import pickle
import warnings
import datetime
warnings.filterwarnings('ignore')

# TensorFlow and Keras
import tensorflow as tf
from tensorflow import keras
from tensorflow.keras.utils import to_categorical

# Scikit-learn
from sklearn.utils import check_random_state
from sklearn.preprocessing import LabelEncoder
from sklearn.metrics import (
    confusion_matrix, classification_report, accuracy_score,
    precision_recall_fscore_support, ConfusionMatrixDisplay, f1_score
)
from sklearn.manifold import TSNE
from sklearn.model_selection import train_test_split
from sklearn.utils.multiclass import type_of_target

# Set random seeds for reproducibility
np.random.seed(42)
tf.random.set_seed(42)

# Set plotting style
sns.set_style('whitegrid')

print("\n✅ All libraries imported successfully!")
print(f"TensorFlow version: {tf.__version__}")
print(f"NumPy version: {np.__version__}")
print(f"Pandas version: {pd.__version__}")

plt.rcdefaults()

# ============================================================================
# LOAD RESHAPED DATA
# ============================================================================

print("📊 Loading augmented and reshaped data...")

# Load the processed data
data = np.load('augmented_reshaped_data_20250728_092500.npz')
reshaped_data = data['reshaped_data']      # Shape: (1232, 10, 4000)
reshaped_labels = data['reshaped_labels']  # Shape: (1232,)

print(f"   ✅ Loaded {len(reshaped_labels)} samples")
print(f"   📊 Input shape per sample: {reshaped_data.shape[1:]}")
print(f"   🏷️  Number of classes: {len(np.unique(reshaped_labels))}")

# ============================================================================
# PREPARE DATA EXACTLY LIKE RUN.PY
# ============================================================================

print("🔄 Preparing data exactly like run.py...")

# Convert labels to proper format if needed
if np.max(reshaped_labels) > 10:
    # Map continuous labels to discrete classes
    unique_labels = np.unique(reshaped_labels)
    label_mapping = {old_label: new_label for new_label, old_label in enumerate(unique_labels)}
    mapped_labels = np.array([label_mapping[label] for label in reshaped_labels])
    print(f"   📝 Mapped {len(unique_labels)} unique labels to classes 0-{len(unique_labels)-1}")
else:
    mapped_labels = reshaped_labels.astype(int)

# Assuming reshaped_data and mapped_labels are defined
input_train = reshaped_data  # Original shape is (1232, 10, 4000)
output_train = mapped_labels

# Split the data into training and validation sets
XXX_train_reshaped, XXX_valid_reshaped, y_train, y_valid = train_test_split(
    input_train, output_train, test_size=0.2, random_state=42
)

# Now, reshape XXX_train and XXX_valid from (N, 10, 4000) to (N, 10, 4000)
XXX_train = XXX_train_reshaped.reshape(XXX_train_reshaped.shape[0], 10, 4000)
XXX_valid = XXX_valid_reshaped.reshape(XXX_valid_reshaped.shape[0], 10, 4000)

# Check and fix label types
print(f"Training data shape: {XXX_train.shape}")
print(f"Training labels shape: {y_train.shape}")
print(f"Validation data shape: {XXX_valid.shape}")
print(f"Validation labels shape: {y_valid.shape}")
print(f"Label type: {type_of_target(y_train)}")
print(f"Label data type: {y_train.dtype if hasattr(y_train, 'dtype') else type(y_train)}")
print(f"Sample labels: {y_train[:10]}")

# Convert labels to proper format if needed
if type_of_target(y_train) == 'continuous':
    print("⚠️  Converting continuous labels to discrete classes...")
    
    # Round to nearest integer if floating point
    if hasattr(y_train, 'dtype') and np.issubdtype(y_train.dtype, np.floating):
        y_train = np.round(y_train).astype(int)
        y_valid = np.round(y_valid).astype(int)
    
    # Ensure labels start from 0
    unique_labels = np.unique(np.concatenate([y_train, y_valid]))
    label_mapping = {old_label: new_label for new_label, old_label in enumerate(unique_labels)}
    
    y_train = np.array([label_mapping[label] for label in y_train])
    y_valid = np.array([label_mapping[label] for label in y_valid])
    
    print(f"✅ Labels converted. New type: {type_of_target(y_train)}")

print(f"Number of classes: {len(np.unique(y_train))}")
print(f"Class distribution: {np.bincount(y_train)}")

# ============================================================================
# LOAD TRAINED MODELS
# ============================================================================

print("\n🤖 Loading trained models...")

models = {}
model_files = {
    'ResNet': 'resnet_model.keras',
    '1D CNN': '1d_cnn_model.keras',
    'LSTM': 'lstm_model.keras',
    'CNN-LSTM': 'cnn_lstm_model.keras'
}

for model_name, file_path in model_files.items():
    try:
        model = tf.keras.models.load_model(file_path)
        models[model_name] = model
        print(f"   ✅ {model_name}: {model.count_params():,} parameters")
        print(f"      Input: {model.input_shape}")
        print(f"      Output: {model.output_shape}")
    except Exception as e:
        print(f"   ❌ {model_name}: {e}")

print(f"📊 Successfully loaded {len(models)}/4 models")

# ============================================================================
# MAKE PREDICTIONS EXACTLY LIKE RUN.PY
# ============================================================================

results = {}

print("\n" + "=" * 60)
print("MODEL COMPARISON RESULTS")
print("=" * 60)

for model_name, model in models.items():
    print(f"\n🔄 Evaluating {model_name}...")
    
    # Measure prediction time
    start_time = time.time()
    
    # Make predictions
    y_pred_train = model.predict(XXX_train)
    y_pred_valid = model.predict(XXX_valid)
    
    # Convert predictions to class labels
    if len(y_pred_train.shape) > 1 and y_pred_train.shape[1] > 1:
        # Multi-class classification
        y_pred_train_classes = np.argmax(y_pred_train, axis=1)
        y_pred_valid_classes = np.argmax(y_pred_valid, axis=1)
    else:
        # Single output
        y_pred_train_classes = np.round(y_pred_train.flatten()).astype(int)
        y_pred_valid_classes = np.round(y_pred_valid.flatten()).astype(int)
        y_pred_train_classes = np.clip(y_pred_train_classes, 0, len(np.unique(y_train))-1)
        y_pred_valid_classes = np.clip(y_pred_valid_classes, 0, len(np.unique(y_train))-1)
    
    prediction_time = time.time() - start_time
    
    # Debug: Check data types and shapes
    print(f"\nDEBUG - {model_name}:")
    print(f"y_train type: {type(y_train)}, shape: {y_train.shape if hasattr(y_train, 'shape') else 'N/A'}")
    print(f"y_train sample: {y_train[:5] if hasattr(y_train, '__getitem__') else 'N/A'}")
    print(f"y_pred_train type: {type(y_pred_train_classes)}, shape: {y_pred_train_classes.shape if hasattr(y_pred_train_classes, 'shape') else 'N/A'}")
    print(f"y_pred_train sample: {y_pred_train_classes[:5] if hasattr(y_pred_train_classes, '__getitem__') else 'N/A'}")
    print(f"y_train unique values: {np.unique(y_train) if hasattr(y_train, '__iter__') else 'N/A'}")
    print(f"y_pred_train unique values: {np.unique(y_pred_train_classes) if hasattr(y_pred_train_classes, '__iter__') else 'N/A'}")
    
    # Calculate metrics
    train_acc = accuracy_score(y_train, y_pred_train_classes)
    valid_acc = accuracy_score(y_valid, y_pred_valid_classes)
    
    # Get detailed metrics for validation set
    precision, recall, f1, _ = precision_recall_fscore_support(
        y_valid, y_pred_valid_classes, average='weighted'
    )
    
    # Get per-class F1 scores for both train and validation
    f1_train_per_class = f1_score(y_train, y_pred_train_classes, average=None)
    f1_valid_per_class = f1_score(y_valid, y_pred_valid_classes, average=None)
    
    # Get classification reports with all labels
    train_report = classification_report(y_train, y_pred_train_classes, output_dict=True)
    valid_report = classification_report(y_valid, y_pred_valid_classes, output_dict=True)
    
    # Generate confusion matrices
    cm_train = confusion_matrix(y_train, y_pred_train_classes)
    cm_valid = confusion_matrix(y_valid, y_pred_valid_classes)
    
    # Display confusion matrices
    fig, axes = plt.subplots(1, 2, figsize=(15, 6))
    
    # Training confusion matrix
    disp_train = ConfusionMatrixDisplay(confusion_matrix=cm_train)
    disp_train.plot(ax=axes[0], cmap='Blues')
    axes[0].set_title(f'{model_name} - Training Set Confusion Matrix')
    
    # Validation confusion matrix
    disp_valid = ConfusionMatrixDisplay(confusion_matrix=cm_valid)
    disp_valid.plot(ax=axes[1], cmap='Blues')
    axes[1].set_title(f'{model_name} - Validation Set Confusion Matrix')
    
    plt.tight_layout()
    
    # Save the confusion matrix plot
    plot_filename = f"{model_name.lower().replace(' ', '_').replace('-', '_')}_confusion_matrices.png"
    plt.savefig(plot_filename, dpi=300, bbox_inches='tight')
    print(f"💾 Confusion matrices saved as: {plot_filename}")
    
    plt.show()
    
    # Display detailed F1 scores
    print(f"\n📊 Detailed F1-Scores for {model_name}:")
    print("=" * 50)
    
    # Create DataFrame for F1 scores by class
    class_names = [f"Class {i}" for i in range(len(f1_train_per_class))]
    f1_df = pd.DataFrame({
        'Class': class_names,
        'F1_Train': f1_train_per_class,
        'F1_Valid': f1_valid_per_class
    })
    f1_df['F1_Difference'] = f1_df['F1_Valid'] - f1_df['F1_Train']
    
    print("Per-Class F1 Scores:")
    print(f1_df.round(4))
    
    # Display full classification reports
    print(f"\n📋 Full Classification Report - Training Set:")
    train_report_df = pd.DataFrame(train_report).transpose()
    print(train_report_df.round(4))

    print(f"\n📋 Full Classification Report - Validation Set:")
    valid_report_df = pd.DataFrame(valid_report).transpose()
    print(valid_report_df.round(4))
    
    # Store results
    results[model_name] = {
        'model': model,
        'train_accuracy': train_acc,
        'valid_accuracy': valid_acc,
        'precision': precision,
        'recall': recall,
        'f1_score': f1,
        'f1_train_per_class': f1_train_per_class,
        'f1_valid_per_class': f1_valid_per_class,
        'train_report': train_report,
        'valid_report': valid_report,
        'confusion_matrix_train': cm_train,
        'confusion_matrix_valid': cm_valid,
        'training_time': 0,  # No training time since models are pre-trained
        'prediction_time': prediction_time,
        'y_pred_train': y_pred_train_classes,
        'y_pred_valid': y_pred_valid_classes,
        'y_pred_train_proba': y_pred_train,
        'y_pred_valid_proba': y_pred_valid,
        'history': None  # No training history for predictions
    }
    
    print(f"✅ {model_name} completed!")
    print(f"   Training Accuracy: {train_acc:.4f}")
    print(f"   Validation Accuracy: {valid_acc:.4f}")
    print(f"   F1-Score: {f1:.4f}")
    print(f"   Prediction Time: {prediction_time:.2f}s")

# ============================================================================
# EXPORT RESULTS EXACTLY LIKE RUN.PY
# ============================================================================

print("\n💾 Exporting results exactly like run.py...")

# Save comprehensive results for later analysis
timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")

# Save main results
results_filename = f'model_comparison_results_{timestamp}.pkl'
with open(results_filename, 'wb') as f:
    pickle.dump(results, f)

# Save summary DataFrame
summary_data = []
for model_name, result in results.items():
    summary_data.append({
        'Model': model_name,
        'Train Accuracy': f"{result['train_accuracy']:.4f}",
        'Valid Accuracy': f"{result['valid_accuracy']:.4f}",
        'Precision': f"{result['precision']:.4f}",
        'Recall': f"{result['recall']:.4f}",
        'F1-Score': f"{result['f1_score']:.4f}",
        'Prediction Time (s)': f"{result['prediction_time']:.2f}"
    })

summary_df = pd.DataFrame(summary_data)
summary_filename = f'model_comparison_summary_{timestamp}.csv'
summary_df.to_csv(summary_filename, index=False)

# Save training data info for loading
data_info = {
    'X_train_shape': XXX_train.shape,
    'X_valid_shape': XXX_valid.shape,
    'y_train_shape': y_train.shape,
    'y_valid_shape': y_valid.shape,
    'n_classes': len(np.unique(y_train)),
    'class_names': [f'Class_{i}' for i in range(len(np.unique(y_train)))],
    'timestamp': timestamp,
    'prediction_mode': True
}

data_info_filename = f'prediction_data_info_{timestamp}.pkl'
with open(data_info_filename, 'wb') as f:
    pickle.dump(data_info, f)

print(f"💾 Main results saved as: {results_filename}")
print(f"💾 Summary saved as: {summary_filename}")
print(f"💾 Data info saved as: {data_info_filename}")

# ============================================================================
# FINAL RANKING - EXACTLY LIKE RUN.PY
# ============================================================================

print("\n" + "="*80)
print("🏆 FINAL MODEL RANKING 🏆")
print("="*80)

model_accuracies = {
    name: result['valid_accuracy'] 
    for name, result in results.items()
}

sorted_models = sorted(model_accuracies.items(), key=lambda x: x[1], reverse=True)

print("Ranking by Validation Accuracy:")
for i, (model_name, accuracy) in enumerate(sorted_models, 1):
    print(f"{i}. {model_name}: {accuracy:.4f}")

print(f"\n🥇 Best Model: {sorted_models[0][0]} with {sorted_models[0][1]:.4f} accuracy")

print("\n🎉 Model prediction and export completed!")

# ============================================================================
# SUMMARY TABLE - EXACTLY LIKE RUN.PY
# ============================================================================

print("\n" + "="*80)
print("SUMMARY TABLE")
print("="*80)
print(summary_df)

print(f"\n✅ SUCCESS! Predictions completed exactly like run.py")
print(f"📊 Processed {len(y_valid)} validation samples")
print(f"🤖 Used {len(models)} models")
print(f"📄 Files created:")
print(f"   - {results_filename}")
print(f"   - {summary_filename}")
print(f"   - {data_info_filename}")
print(f"   - Confusion matrix plots for each model")

print(f"\n🎯 Results exported in exact same format as run.py!")
print(f"📋 Available variables:")
print(f"   - results: Dictionary with all model results")
print(f"   - summary_df: Summary DataFrame")
print(f"   - XXX_train, XXX_valid, y_train, y_valid: Data splits")
print(f"   - models: Dictionary of loaded models")
