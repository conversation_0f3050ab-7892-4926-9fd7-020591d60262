"""
📊 t-SNE VISUALIZATION CALLBACK
Custom Keras callback to export t-SNE visualizations at specific epochs
Exports at epochs: 20, 40, 60, 80, 100
"""

import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.manifold import TSNE
import tensorflow as tf
from tensorflow import keras
import os
import datetime
import warnings
warnings.filterwarnings('ignore')

class TSNEVisualizationCallback(keras.callbacks.Callback):
    """
    Custom callback to generate and save t-SNE visualizations at specific epochs
    """
    
    def __init__(self, X_data, y_data, model_name="Model", 
                 target_epochs=[20, 40, 60, 80, 100], 
                 output_dir="tsne_visualizations",
                 perplexity=30, n_iter=1000, random_state=42):
        """
        Initialize the t-SNE visualization callback
        
        Args:
            X_data: Input data for t-SNE (validation data recommended)
            y_data: Labels for the data
            model_name: Name of the model for file naming
            target_epochs: List of epochs to generate visualizations
            output_dir: Directory to save visualizations
            perplexity: t-SNE perplexity parameter
            n_iter: Number of iterations for t-SNE
            random_state: Random state for reproducibility
        """
        super().__init__()
        self.X_data = X_data
        self.y_data = y_data
        self.model_name = model_name
        self.target_epochs = target_epochs
        self.output_dir = output_dir
        self.perplexity = perplexity
        self.n_iter = n_iter
        self.random_state = random_state
        
        # Create output directory
        os.makedirs(self.output_dir, exist_ok=True)
        
        # Set up plotting style
        plt.style.use('default')
        sns.set_palette("husl")
        
        print(f"📊 t-SNE Callback initialized for {model_name}")
        print(f"   Target epochs: {target_epochs}")
        print(f"   Data shape: {X_data.shape}")
        print(f"   Output directory: {output_dir}")
    
    def on_epoch_end(self, epoch, logs=None):
        """Called at the end of each epoch"""
        current_epoch = epoch + 1  # Keras epochs are 0-indexed
        
        if current_epoch in self.target_epochs:
            print(f"\n📊 Generating t-SNE visualization for epoch {current_epoch}...")
            self._generate_tsne_visualization(current_epoch, logs)
    
    def _extract_features(self, model, X_data):
        """Extract features from the model for t-SNE"""
        try:
            # Try to get features from the last hidden layer before output
            if hasattr(model, 'layers') and len(model.layers) > 1:
                # Find the last dense layer before output
                feature_layer = None
                for i in range(len(model.layers) - 1, -1, -1):
                    layer = model.layers[i]
                    if hasattr(layer, 'units') and layer.units > 1:
                        feature_layer = layer
                        break
                
                if feature_layer is not None:
                    # Create a model that outputs features from this layer
                    feature_model = keras.Model(
                        inputs=model.input,
                        outputs=feature_layer.output
                    )
                    features = feature_model.predict(X_data, verbose=0)
                    print(f"   Extracted features from layer: {feature_layer.name}")
                    print(f"   Feature shape: {features.shape}")
                    return features
            
            # Fallback: use model predictions
            predictions = model.predict(X_data, verbose=0)
            print(f"   Using model predictions as features")
            print(f"   Prediction shape: {predictions.shape}")
            return predictions
            
        except Exception as e:
            print(f"   Warning: Error extracting features: {e}")
            # Final fallback: flatten input data
            return X_data.reshape(X_data.shape[0], -1)
    
    def _generate_tsne_visualization(self, epoch, logs):
        """Generate and save t-SNE visualization"""
        try:
            # Extract features from the current model
            features = self._extract_features(self.model, self.X_data)
            
            # If features are too high-dimensional, reduce first with PCA
            if features.shape[1] > 50:
                from sklearn.decomposition import PCA
                print(f"   Reducing dimensionality with PCA: {features.shape[1]} -> 50")
                pca = PCA(n_components=50, random_state=self.random_state)
                features = pca.fit_transform(features)
            
            # Apply t-SNE
            print(f"   Applying t-SNE...")
            tsne = TSNE(
                n_components=2,
                perplexity=min(self.perplexity, len(features) - 1),
                n_iter=self.n_iter,
                random_state=self.random_state,
                verbose=0
            )
            
            tsne_results = tsne.fit_transform(features)
            
            # Create visualization
            self._create_visualization(tsne_results, epoch, logs)
            
        except Exception as e:
            print(f"   Error generating t-SNE: {e}")
    
    def _create_visualization(self, tsne_results, epoch, logs):
        """Create and save the t-SNE visualization"""
        try:
            # Create figure with subplots
            fig, axes = plt.subplots(1, 2, figsize=(16, 6))
            
            # Get unique classes and colors
            unique_classes = np.unique(self.y_data)
            colors = plt.cm.tab10(np.linspace(0, 1, len(unique_classes)))
            
            # Plot 1: Colored by class
            ax1 = axes[0]
            for i, class_label in enumerate(unique_classes):
                mask = self.y_data == class_label
                ax1.scatter(
                    tsne_results[mask, 0], 
                    tsne_results[mask, 1],
                    c=[colors[i]], 
                    label=f'Class {int(class_label)}',
                    alpha=0.7,
                    s=20
                )
            
            ax1.set_title(f'{self.model_name} - t-SNE at Epoch {epoch}\nColored by Class')
            ax1.set_xlabel('t-SNE Component 1')
            ax1.set_ylabel('t-SNE Component 2')
            ax1.legend(bbox_to_anchor=(1.05, 1), loc='upper left')
            ax1.grid(True, alpha=0.3)
            
            # Plot 2: Density plot
            ax2 = axes[1]
            scatter = ax2.scatter(
                tsne_results[:, 0], 
                tsne_results[:, 1],
                c=self.y_data,
                cmap='viridis',
                alpha=0.7,
                s=20
            )
            
            ax2.set_title(f'{self.model_name} - t-SNE at Epoch {epoch}\nDensity View')
            ax2.set_xlabel('t-SNE Component 1')
            ax2.set_ylabel('t-SNE Component 2')
            ax2.grid(True, alpha=0.3)
            
            # Add colorbar
            cbar = plt.colorbar(scatter, ax=ax2)
            cbar.set_label('Class Label')
            
            # Add training metrics if available
            if logs:
                metrics_text = f"Epoch {epoch}\n"
                if 'loss' in logs:
                    metrics_text += f"Loss: {logs['loss']:.4f}\n"
                if 'accuracy' in logs:
                    metrics_text += f"Accuracy: {logs['accuracy']:.4f}\n"
                if 'val_loss' in logs:
                    metrics_text += f"Val Loss: {logs['val_loss']:.4f}\n"
                if 'val_accuracy' in logs:
                    metrics_text += f"Val Acc: {logs['val_accuracy']:.4f}"
                
                # Add text box with metrics
                ax1.text(0.02, 0.98, metrics_text, transform=ax1.transAxes,
                        verticalalignment='top', bbox=dict(boxstyle='round', 
                        facecolor='wheat', alpha=0.8))
            
            plt.tight_layout()
            
            # Save the visualization
            timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"{self.model_name}_tsne_epoch_{epoch:03d}_{timestamp}.png"
            filepath = os.path.join(self.output_dir, filename)
            
            plt.savefig(filepath, dpi=300, bbox_inches='tight')
            plt.close()
            
            print(f"   ✅ t-SNE visualization saved: {filepath}")
            
            # Also save the t-SNE coordinates
            coords_filename = f"{self.model_name}_tsne_coords_epoch_{epoch:03d}_{timestamp}.npz"
            coords_filepath = os.path.join(self.output_dir, coords_filename)
            
            np.savez_compressed(
                coords_filepath,
                tsne_coordinates=tsne_results,
                labels=self.y_data,
                epoch=epoch,
                model_name=self.model_name
            )
            
            print(f"   ✅ t-SNE coordinates saved: {coords_filepath}")
            
        except Exception as e:
            print(f"   Error creating visualization: {e}")

def create_tsne_callback(X_data, y_data, model_name="Model", 
                        target_epochs=[20, 40, 60, 80, 100],
                        output_dir="tsne_visualizations",
                        perplexity=30, n_iter=1000, random_state=42):
    """
    Factory function to create a t-SNE visualization callback
    
    Args:
        X_data: Input data for t-SNE (validation data recommended)
        y_data: Labels for the data
        model_name: Name of the model for file naming
        target_epochs: List of epochs to generate visualizations
        output_dir: Directory to save visualizations
        perplexity: t-SNE perplexity parameter
        n_iter: Number of iterations for t-SNE
        random_state: Random state for reproducibility
    
    Returns:
        TSNEVisualizationCallback instance
    """
    return TSNEVisualizationCallback(
        X_data=X_data,
        y_data=y_data,
        model_name=model_name,
        target_epochs=target_epochs,
        output_dir=output_dir,
        perplexity=perplexity,
        n_iter=n_iter,
        random_state=random_state
    )

# Example usage function
def example_usage():
    """Example of how to use the t-SNE callback"""
    print("📊 t-SNE Visualization Callback - Example Usage")
    print("="*60)
    
    # Load your data (example)
    # X_valid, y_valid = load_your_validation_data()
    
    # Create the callback
    tsne_callback = create_tsne_callback(
        X_data=None,  # Replace with your X_valid
        y_data=None,  # Replace with your y_valid
        model_name="ResNet",
        target_epochs=[20, 40, 60, 80, 100],
        output_dir="tsne_visualizations",
        perplexity=30,
        n_iter=1000,
        random_state=42
    )
    
    # Use in model training
    # model.fit(
    #     X_train, y_train,
    #     validation_data=(X_valid, y_valid),
    #     epochs=100,
    #     callbacks=[tsne_callback]  # Add the callback here
    # )
    
    print("Usage example created!")

if __name__ == "__main__":
    example_usage()
