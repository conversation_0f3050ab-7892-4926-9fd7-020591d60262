"""
🔍 FILE VERIFICATION SCRIPT
This script checks if all required files are present for model prediction
"""
import os
import numpy as np

def check_file_exists(filepath, description=""):
    """Check if a file exists and return its size"""
    if os.path.exists(filepath):
        size_mb = os.path.getsize(filepath) / (1024*1024)
        status = "✅"
        size_info = f"{size_mb:.1f} MB"
    else:
        status = "❌"
        size_info = "NOT FOUND"
    
    desc_text = f" ({description})" if description else ""
    print(f"{status} {filepath:<40} {size_info}{desc_text}")
    return os.path.exists(filepath)

def verify_data_file(filepath):
    """Verify the data file format and contents"""
    if not os.path.exists(filepath):
        return False
    
    try:
        data = np.load(filepath)
        print(f"   📊 Data file contents:")
        
        required_keys = ['X_train', 'X_valid', 'y_train', 'y_valid']
        all_present = True
        
        for key in required_keys:
            if key in data:
                shape = data[key].shape
                print(f"      {key}: {shape}")
            else:
                print(f"      ❌ Missing: {key}")
                all_present = False
        
        if all_present:
            X_valid = data['X_valid']
            y_valid = data['y_valid']
            print(f"   ✅ Validation samples: {len(y_valid)}")
            print(f"   ✅ Input shape: {X_valid.shape[1:]}")
            print(f"   ✅ Classes: {len(np.unique(y_valid))}")
        
        return all_present
        
    except Exception as e:
        print(f"   ❌ Error reading data file: {e}")
        return False

def verify_model_compatibility():
    """Check TensorFlow availability"""
    try:
        import tensorflow as tf
        print(f"✅ TensorFlow version: {tf.__version__}")
        return True
    except ImportError:
        print("❌ TensorFlow not installed")
        print("   Install with: pip install tensorflow")
        return False

def main():
    """Main verification function"""
    print("🔍 FILE VERIFICATION FOR MODEL PREDICTION")
    print("="*60)
    
    # Check required model files
    print("\n🤖 MODEL FILES:")
    model_files = [
        ('1d_cnn_model.keras', '1D CNN model'),
        ('cnn_lstm_model.keras', 'CNN-LSTM model'),
        ('lstm_model.keras', 'LSTM model'),
        ('resnet_model.keras', 'ResNet model')
    ]
    
    models_present = 0
    for filepath, description in model_files:
        if check_file_exists(filepath, description):
            models_present += 1
    
    # Check data files
    print(f"\n📊 DATA FILES:")
    data_files = [
        ('augmented_data_20250728_091843.npz', 'Augmented dataset'),
        ('READY_TO_RUN_PREDICTIONS_20250728_091751.py', 'Prediction script')
    ]
    
    data_present = 0
    for filepath, description in data_files:
        if check_file_exists(filepath, description):
            data_present += 1
    
    # Verify data file contents
    print(f"\n📈 DATA FILE VERIFICATION:")
    data_file = 'augmented_data_20250728_091843.npz'
    data_valid = verify_data_file(data_file)
    
    # Check alternative data source
    print(f"\n📁 ALTERNATIVE DATA SOURCE:")
    data_dir = 'data'
    if os.path.exists(data_dir):
        mat_files = [f for f in os.listdir(data_dir) if f.endswith('.mat')]
        print(f"✅ data/ directory: {len(mat_files)} .mat files")
        for mat_file in sorted(mat_files):
            filepath = os.path.join(data_dir, mat_file)
            size_mb = os.path.getsize(filepath) / (1024*1024)
            print(f"   - {mat_file}: {size_mb:.1f} MB")
    else:
        print("❌ data/ directory: NOT FOUND")
    
    # Check optional files
    print(f"\n📋 OPTIONAL FILES:")
    optional_files = [
        ('model_comparison_results_20250628_001945.pkl', 'Previous results'),
        ('fixed_data_loader.py', 'Data loader script'),
        ('load_data.py', 'Core data functions')
    ]
    
    for filepath, description in optional_files:
        check_file_exists(filepath, description)
    
    # Check environment
    print(f"\n🔧 ENVIRONMENT:")
    tf_available = verify_model_compatibility()
    
    # Summary
    print(f"\n" + "="*60)
    print("📋 VERIFICATION SUMMARY")
    print("="*60)
    
    print(f"🤖 Models available: {models_present}/4")
    if models_present == 4:
        print("   ✅ All models present")
    else:
        print("   ⚠️  Some models missing")
    
    print(f"📊 Data files: {data_present}/2")
    if data_present >= 1:
        print("   ✅ Prediction can proceed")
    else:
        print("   ❌ Missing required files")
    
    print(f"📈 Data format: {'✅ Valid' if data_valid else '❌ Invalid'}")
    print(f"🔧 TensorFlow: {'✅ Available' if tf_available else '❌ Missing'}")
    
    # Overall status
    ready_to_predict = (models_present == 4 and data_present >= 1 and data_valid and tf_available)
    
    print(f"\n🎯 PREDICTION READY: {'✅ YES' if ready_to_predict else '❌ NO'}")
    
    if ready_to_predict:
        print("\n🚀 YOU'RE READY TO PREDICT!")
        print("Run: python READY_TO_RUN_PREDICTIONS_20250728_091751.py")
    else:
        print("\n⚠️  MISSING REQUIREMENTS:")
        if models_present < 4:
            print("   - Download missing model files")
        if data_present < 1:
            print("   - Ensure data files are present")
        if not data_valid:
            print("   - Check data file format")
        if not tf_available:
            print("   - Install TensorFlow: pip install tensorflow")
    
    return ready_to_predict

if __name__ == "__main__":
    ready = main()
    
    if ready:
        print(f"\n💡 Next step: Run predictions to classify 247 validation samples!")
    else:
        print(f"\n💡 Fix the issues above, then run this script again.")
