"""
🔧 FIXED DATA LOADER WITH PROPER AUGMENTATION
This script fixes the augmentation issues and loads the complete dataset
"""
import os
import numpy as np
import random
from scipy.io import loadmat
from sklearn.model_selection import train_test_split
from sklearn.utils.multiclass import type_of_target

def load_mat_data(directory='data'):
    """Load all .mat files from directory"""
    all_data = {}
    
    # Iterate over all files in the directory
    for filename in os.listdir(directory):
        if filename.endswith('.mat'):
            filepath = os.path.join(directory, filename)
            # Load the .mat file and add its contents to the dictionary
            mat_data = loadmat(filepath)
            
            # Use filename (without extension) as key for the data
            key = os.path.splitext(filename)[0]
            all_data[key] = mat_data['acceleration']
    
    return all_data

def prepare_input_data(all_data):
    """Prepare input data and labels"""
    keys_to_stack = [f'NamO{i}' for i in range(11)]
    input_data = np.stack([all_data[key] for key in keys_to_stack], axis=0)
    
    # Create the corresponding labels
    output_labels = np.linspace(0, 28, 11)
    
    # Truncate to 40000 samples
    input_data = input_data[:, :, :40000]
    
    print(f"Input data shape: {input_data.shape}")
    print(f"Output labels shape: {output_labels.shape}")
    
    return input_data, output_labels

def augment_time_series_data_fixed(input_data, labels, num_augmentations=4):
    """Fixed augment time series data - ensures consistent shapes"""
    print("🔧 Applying data augmentation (fixed version)...")
    
    augmented_data = []
    augmented_labels = []

    num_samples, num_channels, sequence_length = input_data.shape
    print(f"Original data shape: {input_data.shape}")

    for i in range(num_samples):
        for aug_idx in range(num_augmentations):
            # Choose a random augmentation technique
            augmentation_type = random.choice(['noise', 'reverse', 'crop_pad'])

            if augmentation_type == 'noise':
                # Add random noise
                noise = np.random.normal(0, 0.001, input_data[i].shape)
                augmented_sample = input_data[i] + noise

            elif augmentation_type == 'reverse':
                # Reverse the sequence
                augmented_sample = np.flip(input_data[i], axis=-1)

            elif augmentation_type == 'crop_pad':
                # Fixed crop and pad - maintain exact sequence length
                crop_size = random.randint(1, min(100, sequence_length // 100))
                
                # Crop from the beginning and pad at the end
                cropped_sample = input_data[i][:, crop_size:]
                
                # Pad to maintain original length
                pad_width = ((0, 0), (0, crop_size))
                augmented_sample = np.pad(cropped_sample, pad_width, mode='constant', constant_values=0)
                
                # Ensure exact shape match
                augmented_sample = augmented_sample[:, :sequence_length]

            # Verify shape consistency
            if augmented_sample.shape != input_data[i].shape:
                print(f"⚠️  Shape mismatch detected! Expected {input_data[i].shape}, got {augmented_sample.shape}")
                # Fallback to original sample
                augmented_sample = input_data[i].copy()

            augmented_data.append(augmented_sample)
            augmented_labels.append(labels[i])

    # Convert to numpy arrays - this should work now with consistent shapes
    try:
        augmented_data = np.array(augmented_data)
        augmented_labels = np.array(augmented_labels)
        
        print(f"✅ Augmented data shape: {augmented_data.shape}")
        print(f"✅ Augmented labels shape: {augmented_labels.shape}")
        
    except ValueError as e:
        print(f"❌ Error creating augmented arrays: {e}")
        print("🔄 Falling back to list-based storage...")
        
        # If array conversion fails, check shapes and fix
        shapes = [sample.shape for sample in augmented_data]
        unique_shapes = list(set(shapes))
        print(f"Found {len(unique_shapes)} unique shapes: {unique_shapes}")
        
        # Fix inconsistent shapes
        target_shape = input_data[0].shape
        fixed_data = []
        
        for sample in augmented_data:
            if sample.shape == target_shape:
                fixed_data.append(sample)
            else:
                # Resize to target shape
                if sample.shape[1] > target_shape[1]:
                    # Truncate
                    fixed_sample = sample[:, :target_shape[1]]
                else:
                    # Pad
                    pad_width = ((0, 0), (0, target_shape[1] - sample.shape[1]))
                    fixed_sample = np.pad(sample, pad_width, mode='constant', constant_values=0)
                fixed_data.append(fixed_sample)
        
        augmented_data = np.array(fixed_data)
        augmented_labels = np.array(augmented_labels)
        
        print(f"✅ Fixed augmented data shape: {augmented_data.shape}")
        print(f"✅ Fixed augmented labels shape: {augmented_labels.shape}")

    return augmented_data, augmented_labels

def reshape_time_series_data_v8(input_data, label_data, segments_per_new_sample, segment_length):
    """Reshape time series data and corresponding labels into a specified shape"""
    num_samples_original, num_channels, length_original = input_data.shape

    # Validate the feasibility of reshaping
    if length_original % segment_length != 0:
        print(f"⚠️  Warning: Segment length {segment_length} does not evenly divide original length {length_original}")
        print(f"   Truncating to {(length_original // segment_length) * segment_length}")
        input_data = input_data[:, :, :(length_original // segment_length) * segment_length]
        length_original = input_data.shape[2]

    total_segments_per_original_sample = (length_original // segment_length) * num_channels
    num_samples_new = (num_samples_original * total_segments_per_original_sample) // segments_per_new_sample

    # Validate if reshaping is possible
    if (num_samples_original * total_segments_per_original_sample) % segments_per_new_sample != 0:
        print(f"⚠️  Warning: Reshaping not perfectly divisible. Some data will be truncated.")
        num_samples_new = (num_samples_original * total_segments_per_original_sample) // segments_per_new_sample

    # Initialize reshaped data and labels
    new_shape = (num_samples_new, segments_per_new_sample, segment_length)
    reshaped_data = np.zeros(new_shape)
    reshaped_labels = np.zeros(num_samples_new)

    # Reshape the data and labels
    count = 0
    for i in range(num_samples_original):
        segment_count = 0
        for j in range(num_channels):
            for k in range(length_original // segment_length):
                if count >= num_samples_new:
                    break
                    
                start_idx = k * segment_length
                end_idx = start_idx + segment_length
                reshaped_data[count, segment_count % segments_per_new_sample, :] = input_data[i, j, start_idx:end_idx]
                
                if (segment_count + 1) % segments_per_new_sample == 0:
                    reshaped_labels[count] = label_data[i]
                    count += 1
                segment_count += 1
            
            if count >= num_samples_new:
                break
        
        if count >= num_samples_new:
            break

    print(f"Reshaped data shape: {reshaped_data.shape}")
    print(f"Reshaped labels shape: {reshaped_labels.shape}")

    return reshaped_data, reshaped_labels

def prepare_train_test_data(reshaped_data, reshaped_labels, test_size=0.2, random_state=42):
    """Prepare training and testing data"""
    # Split the data into training and validation sets
    XXX_train_reshaped, XXX_valid_reshaped, y_train, y_valid = train_test_split(
        reshaped_data, reshaped_labels, test_size=test_size, random_state=random_state
    )

    # Reshape data
    XXX_train = XXX_train_reshaped.reshape(XXX_train_reshaped.shape[0], 10, 4000)
    XXX_valid = XXX_valid_reshaped.reshape(XXX_valid_reshaped.shape[0], 10, 4000)

    print(f"Training data shape: {XXX_train.shape}")
    print(f"Training labels shape: {y_train.shape}")
    print(f"Validation data shape: {XXX_valid.shape}")
    print(f"Validation labels shape: {y_valid.shape}")
    print(f"Label type: {type_of_target(y_train)}")
    print(f"Sample labels: {y_train[:10]}")

    # Convert labels to proper format if needed
    if type_of_target(y_train) == 'continuous':
        print("⚠️  Converting continuous labels to discrete classes...")
        
        # Round to nearest integer if floating point
        if hasattr(y_train, 'dtype') and np.issubdtype(y_train.dtype, np.floating):
            y_train = np.round(y_train).astype(int)
            y_valid = np.round(y_valid).astype(int)
        
        # Ensure labels start from 0
        unique_labels = np.unique(np.concatenate([y_train, y_valid]))
        label_mapping = {old_label: new_label for new_label, old_label in enumerate(unique_labels)}
        
        y_train = np.array([label_mapping[label] for label in y_train])
        y_valid = np.array([label_mapping[label] for label in y_valid])
        
        print(f"✅ Labels converted. New type: {type_of_target(y_train)}")

    print(f"Number of classes: {len(np.unique(y_train))}")
    print(f"Class distribution: {np.bincount(y_train)}")

    return XXX_train, XXX_valid, y_train, y_valid

def load_complete_dataset_with_augmentation():
    """Load complete dataset WITH proper augmentation"""
    print("🚀 LOADING COMPLETE DATASET WITH AUGMENTATION")
    print("="*60)
    
    try:
        # Load raw data
        print("1. Loading .mat files...")
        all_data = load_mat_data('data')
        print(f"   ✅ Loaded {len(all_data)} data files")
        
        # Prepare input data
        print("\n2. Preparing input data...")
        input_data, output_labels = prepare_input_data(all_data)
        
        # Apply augmentation (FIXED VERSION)
        print("\n3. Augmenting time series data (FIXED)...")
        augmented_data, augmented_labels = augment_time_series_data_fixed(
            input_data, output_labels, num_augmentations=4
        )
        
        # Reshape data
        print("\n4. Reshaping data...")
        reshaped_data, reshaped_labels = reshape_time_series_data_v8(
            augmented_data, augmented_labels, 
            segments_per_new_sample=10, 
            segment_length=4000
        )
        
        # Prepare train/test split
        print("\n5. Preparing train/test data...")
        X_train, X_valid, y_train, y_valid = prepare_train_test_data(
            reshaped_data, reshaped_labels, test_size=0.2, random_state=42
        )
        
        # Create complete data dictionary
        data_dict = {
            'raw_data': all_data,
            'input_data': input_data,
            'output_labels': output_labels,
            'augmented_data': augmented_data,
            'augmented_labels': augmented_labels,
            'reshaped_data': reshaped_data,
            'reshaped_labels': reshaped_labels,
            'X_train': X_train,
            'X_valid': X_valid,
            'y_train': y_train,
            'y_valid': y_valid
        }
        
        print("\n✅ Data preparation completed successfully WITH AUGMENTATION!")
        return data_dict
        
    except Exception as e:
        print(f"❌ Error in data preparation: {str(e)}")
        import traceback
        traceback.print_exc()
        return None

def main():
    """Main function to load data with augmentation"""
    data = load_complete_dataset_with_augmentation()
    
    if data:
        print("\n" + "="*60)
        print("📊 FINAL DATASET SUMMARY (WITH AUGMENTATION)")
        print("="*60)
        print(f"Raw data files: {len(data['raw_data'])}")
        print(f"Original input shape: {data['input_data'].shape}")
        print(f"Augmented data shape: {data['augmented_data'].shape}")
        print(f"Final training shape: {data['X_train'].shape}")
        print(f"Final validation shape: {data['X_valid'].shape}")
        print(f"Number of classes: {len(np.unique(data['y_train']))}")
        print(f"Augmentation factor: {data['augmented_data'].shape[0] / data['input_data'].shape[0]:.1f}x")
        
        print(f"\n🎯 READY FOR PREDICTIONS:")
        print(f"   Training samples: {data['X_train'].shape[0]}")
        print(f"   Validation samples: {data['X_valid'].shape[0]}")
        print(f"   Input shape per sample: {data['X_train'].shape[1:]}")
        
    return data

if __name__ == "__main__":
    # Set random seed for reproducible augmentation
    random.seed(42)
    np.random.seed(42)
    
    data = main()
    
    if data:
        print(f"\n✅ SUCCESS! Data loaded with proper augmentation")
        print(f"Available in 'data' variable with keys:")
        for key in data.keys():
            if hasattr(data[key], 'shape'):
                print(f"   data['{key}']: {data[key].shape}")
            else:
                print(f"   data['{key}']: {type(data[key])}")
