# Export Summary Report
Generated: 2025-07-28 08:58:02

## Dataset Information
- Training samples: 246
- Validation samples: 62
- Input shape: [10, 4000]
- Number of classes: 11
- Data type: float64
- Label range: 0 to 10

## Data Statistics
- Mean: 0.000000
- Std: 0.000720
- Min: -0.006811
- Max: 0.007047

## Available Models (4)
- 1d_cnn_model: 23.5 MB
- cnn_lstm_model: 24.3 MB
- lstm_model: 12.3 MB
- resnet_model: 36.2 MB

Total model size: 96.3 MB

## Files Exported
- data/X_train.npy - Training data
- data/X_valid.npy - Validation data  
- data/y_train.npy - Training labels
- data/y_valid.npy - Validation labels
- data/complete_dataset.npz - Complete compressed dataset
- data/data_info.json - Dataset information
- models/model_info.json - Model information
- models/load_models.py - Model loading script
- csv/train_labels.csv - Training labels
- csv/valid_labels.csv - Validation labels
- csv/sample_statistics.csv - Sample statistics

## Usage
```python
# Load data
import numpy as np
data = np.load('data/complete_dataset.npz')
X_train = data['X_train']
X_valid = data['X_valid']
y_train = data['y_train']
y_valid = data['y_valid']

# Load models
exec(open('models/load_models.py').read())
```
