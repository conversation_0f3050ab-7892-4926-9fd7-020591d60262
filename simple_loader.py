import os
import numpy as np
import pickle
from load_data import (
    load_mat_data, 
    prepare_input_data, 
    augment_time_series_data,
    reshape_time_series_data_v8,
    prepare_train_test_data
)

def load_and_prepare_data():
    """Load and prepare the data using functions from load_data.py"""
    print("🔄 Loading and preparing data...")
    
    try:
        # Load raw data
        print("1. Loading .mat files...")
        all_data = load_mat_data('data')
        print(f"   ✅ Loaded {len(all_data)} data files")
        
        # Show what data files we have
        print("   Available data files:")
        for key in sorted(all_data.keys()):
            shape = all_data[key].shape
            print(f"     - {key}: {shape}")
        
        # Prepare input data
        print("2. Preparing input data...")
        input_data, output_labels = prepare_input_data(all_data)
        
        # Augment data
        print("3. Augmenting time series data...")
        augmented_data, augmented_labels = augment_time_series_data(
            input_data, output_labels, num_augmentations=4
        )
        
        # Reshape data
        print("4. Reshaping data...")
        reshaped_data, reshaped_labels = reshape_time_series_data_v8(
            augmented_data, augmented_labels, 
            segments_per_new_sample=10, 
            segment_length=4000
        )
        
        # Prepare train/test split
        print("5. Preparing train/test data...")
        X_train, X_valid, y_train, y_valid = prepare_train_test_data(
            reshaped_data, reshaped_labels, test_size=0.2, random_state=42
        )
        
        data_dict = {
            'raw_data': all_data,
            'input_data': input_data,
            'output_labels': output_labels,
            'augmented_data': augmented_data,
            'augmented_labels': augmented_labels,
            'reshaped_data': reshaped_data,
            'reshaped_labels': reshaped_labels,
            'X_train': X_train,
            'X_valid': X_valid,
            'y_train': y_train,
            'y_valid': y_valid
        }
        
        print("✅ Data preparation completed successfully!")
        return data_dict
        
    except Exception as e:
        print(f"❌ Error in data preparation: {str(e)}")
        import traceback
        traceback.print_exc()
        return None

def load_results_safe():
    """Safely load the model comparison results from pickle file"""
    results_file = 'model_comparison_results_20250628_001945.pkl'
    
    if not os.path.exists(results_file):
        print(f"⚠️  Results file not found: {results_file}")
        return None
    
    print(f"🔄 Attempting to load results from: {results_file}")
    
    # Try different loading strategies
    try:
        with open(results_file, 'rb') as f:
            results = pickle.load(f)
        print(f"✅ Successfully loaded results from: {results_file}")
        
        # Display results structure
        if isinstance(results, dict):
            print("📊 Results structure:")
            for key, value in results.items():
                if isinstance(value, (list, np.ndarray)):
                    print(f"  {key}: {type(value).__name__} with length {len(value)}")
                elif isinstance(value, dict):
                    print(f"  {key}: dict with {len(value)} keys")
                else:
                    print(f"  {key}: {type(value).__name__}")
        
        return results
        
    except (AttributeError, ModuleNotFoundError) as e:
        print(f"⚠️  Standard pickle loading failed: {str(e)}")
        print("   Attempting to load with safe unpickler...")
        
        # Try to load with a custom unpickler that ignores missing classes
        try:
            class SafeUnpickler(pickle.Unpickler):
                def find_class(self, module, name):
                    # Return a dummy class for missing classes
                    if module == '__main__':
                        print(f"     Creating dummy class for: {name}")
                        return type(name, (), {'__module__': module, '__name__': name})
                    return super().find_class(module, name)
            
            with open(results_file, 'rb') as f:
                results = SafeUnpickler(f).load()
            
            print(f"✅ Successfully loaded results with safe unpickler")
            print("⚠️  Note: Some custom objects may be replaced with dummy objects")
            
            # Display what we could load
            if isinstance(results, dict):
                print("📊 Available keys:")
                for key in results.keys():
                    print(f"  - {key}")
            
            return results
            
        except Exception as e2:
            print(f"❌ Safe unpickler also failed: {str(e2)}")
            return None
            
    except Exception as e:
        print(f"❌ Error loading results: {str(e)}")
        return None

def check_model_files():
    """Check what model files are available"""
    model_files = [
        '1d_cnn_model.keras',
        'cnn_lstm_model.keras', 
        'lstm_model.keras',
        'resnet_model.keras'
    ]
    
    print("🔍 Checking model files...")
    available_models = []
    
    for model_file in model_files:
        if os.path.exists(model_file):
            size = os.path.getsize(model_file)
            size_mb = size / (1024 * 1024)
            print(f"  ✅ {model_file}: {size_mb:.1f} MB")
            available_models.append(model_file)
        else:
            print(f"  ❌ {model_file}: Not found")
    
    return available_models

def main():
    """Main function to load data and check models"""
    print("🚀 Simple Data and Model Loader")
    print("="*50)
    
    # Check model files
    available_models = check_model_files()
    
    # Load results
    results = load_results_safe()
    
    # Load and prepare data
    data_dict = load_and_prepare_data()
    
    print("\n" + "="*50)
    print("📋 SUMMARY")
    print("="*50)
    print(f"Available model files: {len(available_models)}")
    for model in available_models:
        print(f"  - {model}")
    
    print(f"Results loaded: {'✅ Yes' if results else '❌ No'}")
    print(f"Data loaded: {'✅ Yes' if data_dict else '❌ No'}")
    
    if data_dict:
        print("\n📊 Data shapes:")
        print(f"  Training data: {data_dict['X_train'].shape}")
        print(f"  Validation data: {data_dict['X_valid'].shape}")
        print(f"  Training labels: {data_dict['y_train'].shape}")
        print(f"  Validation labels: {data_dict['y_valid'].shape}")
        print(f"  Number of classes: {len(np.unique(data_dict['y_train']))}")
    
    return {
        'available_models': available_models,
        'results': results,
        'data': data_dict
    }

if __name__ == "__main__":
    # Load everything
    loaded_data = main()
    
    # Make variables available in global scope for interactive use
    available_models = loaded_data['available_models']
    results = loaded_data['results']
    data = loaded_data['data']
    
    print("\n" + "="*50)
    print("✅ LOADING COMPLETE!")
    print("="*50)
    print("Available variables:")
    print("  - available_models: List of available .keras model files")
    print("  - results: Loaded pickle results (if successful)")
    print("  - data: Dictionary with all data (raw, processed, train/test)")
    print("\nTo load models with TensorFlow, run:")
    print("  import tensorflow as tf")
    print("  model = tf.keras.models.load_model('lstm_model.keras')")
