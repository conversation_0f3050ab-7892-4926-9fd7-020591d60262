"""
🔮 RUN MODELS WITH t-SNE VISUALIZATION CALLBACK
This script demonstrates how to use the t-SNE callback with your existing models
Exports t-SNE visualizations at epochs 20, 40, 60, 80, 100
"""

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
import tensorflow as tf
from tensorflow import keras
from sklearn.model_selection import train_test_split
from sklearn.utils.multiclass import type_of_target
import warnings
warnings.filterwarnings('ignore')

# Import the t-SNE callback
from tsne_visualization_callback import create_tsne_callback

def load_data_for_tsne():
    """Load the reshaped data for t-SNE visualization"""
    print("📊 Loading data for t-SNE visualization...")
    
    try:
        # Load the processed data
        data = np.load('augmented_reshaped_data_20250728_092500.npz')
        reshaped_data = data['reshaped_data']      # Shape: (1232, 10, 4000)
        reshaped_labels = data['reshaped_labels']  # Shape: (1232,)
        
        print(f"   ✅ Loaded {len(reshaped_labels)} samples")
        print(f"   📊 Input shape per sample: {reshaped_data.shape[1:]}")
        print(f"   🏷️  Number of classes: {len(np.unique(reshaped_labels))}")
        
        return reshaped_data, reshaped_labels
        
    except Exception as e:
        print(f"   ❌ Error loading data: {e}")
        return None, None

def prepare_data_for_training(reshaped_data, reshaped_labels):
    """Prepare data for training with t-SNE callback"""
    print("🔄 Preparing data for training...")
    
    # Convert labels to proper format if needed
    if np.max(reshaped_labels) > 10:
        unique_labels = np.unique(reshaped_labels)
        label_mapping = {old_label: new_label for new_label, old_label in enumerate(unique_labels)}
        mapped_labels = np.array([label_mapping[label] for label in reshaped_labels])
        print(f"   📝 Mapped {len(unique_labels)} unique labels to classes 0-{len(unique_labels)-1}")
    else:
        mapped_labels = reshaped_labels.astype(int)

    # Split the data
    X_train, X_valid, y_train, y_valid = train_test_split(
        reshaped_data, mapped_labels, test_size=0.2, random_state=42
    )
    
    # Convert labels to proper format if needed
    if type_of_target(y_train) == 'continuous':
        print("⚠️  Converting continuous labels to discrete classes...")
        
        if hasattr(y_train, 'dtype') and np.issubdtype(y_train.dtype, np.floating):
            y_train = np.round(y_train).astype(int)
            y_valid = np.round(y_valid).astype(int)
        
        unique_labels = np.unique(np.concatenate([y_train, y_valid]))
        label_mapping = {old_label: new_label for new_label, old_label in enumerate(unique_labels)}
        
        y_train = np.array([label_mapping[label] for label in y_train])
        y_valid = np.array([label_mapping[label] for label in y_valid])
        
        print(f"✅ Labels converted. New type: {type_of_target(y_train)}")

    print(f"Training data shape: {X_train.shape}")
    print(f"Validation data shape: {X_valid.shape}")
    print(f"Number of classes: {len(np.unique(y_train))}")
    print(f"Class distribution: {np.bincount(y_train)}")
    
    return X_train, X_valid, y_train, y_valid

def create_sample_model(input_shape, num_classes, model_type="simple_cnn"):
    """Create a sample model for demonstration"""
    print(f"🤖 Creating {model_type} model...")
    
    if model_type == "simple_cnn":
        model = keras.Sequential([
            keras.layers.Conv1D(32, 3, activation='relu', input_shape=input_shape),
            keras.layers.MaxPooling1D(2),
            keras.layers.Conv1D(64, 3, activation='relu'),
            keras.layers.MaxPooling1D(2),
            keras.layers.Conv1D(128, 3, activation='relu'),
            keras.layers.GlobalAveragePooling1D(),
            keras.layers.Dense(128, activation='relu'),
            keras.layers.Dropout(0.5),
            keras.layers.Dense(64, activation='relu'),  # Feature layer for t-SNE
            keras.layers.Dense(num_classes, activation='softmax')
        ])
    
    elif model_type == "lstm":
        model = keras.Sequential([
            keras.layers.LSTM(64, return_sequences=True, input_shape=input_shape),
            keras.layers.LSTM(32, return_sequences=False),
            keras.layers.Dense(64, activation='relu'),  # Feature layer for t-SNE
            keras.layers.Dropout(0.5),
            keras.layers.Dense(num_classes, activation='softmax')
        ])
    
    else:  # simple_dense
        model = keras.Sequential([
            keras.layers.Flatten(input_shape=input_shape),
            keras.layers.Dense(256, activation='relu'),
            keras.layers.Dropout(0.5),
            keras.layers.Dense(128, activation='relu'),
            keras.layers.Dropout(0.3),
            keras.layers.Dense(64, activation='relu'),  # Feature layer for t-SNE
            keras.layers.Dense(num_classes, activation='softmax')
        ])
    
    model.compile(
        optimizer='adam',
        loss='sparse_categorical_crossentropy',
        metrics=['accuracy']
    )
    
    print(f"   ✅ Model created with {model.count_params():,} parameters")
    return model

def train_model_with_tsne(model, X_train, X_valid, y_train, y_valid, 
                         model_name="Model", epochs=100):
    """Train a model with t-SNE visualization callback"""
    print(f"\n🚀 Training {model_name} with t-SNE visualization...")
    
    # Create t-SNE callback
    tsne_callback = create_tsne_callback(
        X_data=X_valid,  # Use validation data for t-SNE
        y_data=y_valid,
        model_name=model_name,
        target_epochs=[20, 40, 60, 80, 100],
        output_dir=f"tsne_visualizations_{model_name.lower()}",
        perplexity=30,
        n_iter=1000,
        random_state=42
    )
    
    # Other callbacks
    early_stopping = keras.callbacks.EarlyStopping(
        monitor='val_loss',
        patience=15,
        restore_best_weights=True
    )
    
    reduce_lr = keras.callbacks.ReduceLROnPlateau(
        monitor='val_loss',
        factor=0.5,
        patience=10,
        min_lr=1e-7
    )
    
    # Train the model
    history = model.fit(
        X_train, y_train,
        validation_data=(X_valid, y_valid),
        epochs=epochs,
        batch_size=32,
        callbacks=[tsne_callback, early_stopping, reduce_lr],
        verbose=1
    )
    
    return history

def demonstrate_tsne_callback():
    """Demonstrate the t-SNE callback with sample models"""
    print("🔮 DEMONSTRATING t-SNE VISUALIZATION CALLBACK")
    print("="*70)
    
    # Load data
    reshaped_data, reshaped_labels = load_data_for_tsne()
    if reshaped_data is None:
        print("❌ Cannot proceed without data")
        return
    
    # Prepare data
    X_train, X_valid, y_train, y_valid = prepare_data_for_training(
        reshaped_data, reshaped_labels
    )
    
    input_shape = X_train.shape[1:]  # (10, 4000)
    num_classes = len(np.unique(y_train))
    
    print(f"\n📊 Data prepared:")
    print(f"   Input shape: {input_shape}")
    print(f"   Number of classes: {num_classes}")
    print(f"   Training samples: {len(X_train)}")
    print(f"   Validation samples: {len(X_valid)}")
    
    # Train different models with t-SNE visualization
    models_to_train = [
        ("Simple_CNN", "simple_cnn"),
        ("LSTM", "lstm"),
        ("Dense", "simple_dense")
    ]
    
    for model_name, model_type in models_to_train:
        print(f"\n{'='*70}")
        print(f"🤖 TRAINING {model_name} WITH t-SNE VISUALIZATION")
        print(f"{'='*70}")
        
        # Create model
        model = create_sample_model(input_shape, num_classes, model_type)
        
        # Train with t-SNE callback
        history = train_model_with_tsne(
            model, X_train, X_valid, y_train, y_valid,
            model_name=model_name, epochs=100
        )
        
        # Save the trained model
        model.save(f"{model_name.lower()}_with_tsne.keras")
        print(f"   ✅ Model saved: {model_name.lower()}_with_tsne.keras")
        
        print(f"   🎯 Final validation accuracy: {max(history.history['val_accuracy']):.4f}")

def use_tsne_with_existing_model():
    """Example of using t-SNE callback with an existing trained model"""
    print("\n🔄 USING t-SNE WITH EXISTING MODEL")
    print("="*50)
    
    # Load data
    reshaped_data, reshaped_labels = load_data_for_tsne()
    if reshaped_data is None:
        return
    
    X_train, X_valid, y_train, y_valid = prepare_data_for_training(
        reshaped_data, reshaped_labels
    )
    
    # Try to load an existing model
    try:
        model = keras.models.load_model('resnet_model.keras')
        print("   ✅ Loaded existing ResNet model")
        
        # Create t-SNE callback for the existing model
        tsne_callback = create_tsne_callback(
            X_data=X_valid,
            y_data=y_valid,
            model_name="Existing_ResNet",
            target_epochs=[1, 5, 10],  # Shorter epochs for demonstration
            output_dir="tsne_existing_model",
            perplexity=30,
            n_iter=1000,
            random_state=42
        )
        
        # Fine-tune for a few epochs with t-SNE visualization
        print("   🔄 Fine-tuning with t-SNE visualization...")
        model.compile(
            optimizer=keras.optimizers.Adam(learning_rate=1e-5),
            loss='sparse_categorical_crossentropy',
            metrics=['accuracy']
        )
        
        history = model.fit(
            X_train, y_train,
            validation_data=(X_valid, y_valid),
            epochs=10,
            batch_size=32,
            callbacks=[tsne_callback],
            verbose=1
        )
        
        print("   ✅ Fine-tuning with t-SNE completed!")
        
    except Exception as e:
        print(f"   ⚠️  Could not load existing model: {e}")
        print("   💡 Run the demonstration instead")

def main():
    """Main function"""
    print("📊 t-SNE VISUALIZATION CALLBACK DEMO")
    print("="*70)
    
    choice = input("""
Choose an option:
1. Demonstrate t-SNE callback with new models
2. Use t-SNE callback with existing model
3. Both

Enter choice (1/2/3): """).strip()
    
    if choice in ['1', '3']:
        demonstrate_tsne_callback()
    
    if choice in ['2', '3']:
        use_tsne_with_existing_model()
    
    print(f"\n🎉 t-SNE visualization demo completed!")
    print(f"📁 Check the tsne_visualizations_* directories for results")

if __name__ == "__main__":
    main()
