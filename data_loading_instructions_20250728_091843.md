# LOADING INSTRUCTIONS FOR AUGMENTED DATA

## Load the augmented data:
```python
import numpy as np

# Load augmented data
data = np.load('augmented_data_20250728_091843.npz')
X_train = data['X_train']  # Shape: (985, 10, 4000)
X_valid = data['X_valid']  # Shape: (247, 10, 4000)
y_train = data['y_train']  # Shape: (985,)
y_valid = data['y_valid']  # Shape: (247,)

print(f"Loaded augmented dataset:")
print(f"  Training: {X_train.shape}")
print(f"  Validation: {X_valid.shape}")
print(f"  Classes: {len(np.unique(y_train))}")
```

## Dataset Information:
- Original samples: 11
- Augmented samples: 44
- Augmentation factor: 4.0x
- Training samples: 985
- Validation samples: 247
- Classes: 11
