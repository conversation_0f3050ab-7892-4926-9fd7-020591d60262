"""
Complete Data and Model Loading Summary
This script loads all data and provides comprehensive information about models and results.
"""
import os
import numpy as np
from load_data import (
    load_mat_data, 
    prepare_input_data, 
    augment_time_series_data,
    reshape_time_series_data_v8,
    prepare_train_test_data
)

def load_complete_dataset():
    """Load and prepare the complete dataset"""
    print("🔄 LOADING COMPLETE DATASET")
    print("="*60)
    
    try:
        # Load raw data
        print("1. Loading .mat files...")
        all_data = load_mat_data('data')
        print(f"   ✅ Loaded {len(all_data)} data files")
        
        # Show raw data structure
        print("   📊 Raw data structure:")
        for key in sorted(all_data.keys()):
            shape = all_data[key].shape
            size_mb = all_data[key].nbytes / (1024 * 1024)
            print(f"     - {key}: {shape} ({size_mb:.1f} MB)")
        
        # Prepare input data
        print("\n2. Preparing input data...")
        input_data, output_labels = prepare_input_data(all_data)
        print(f"   Input data shape: {input_data.shape}")
        print(f"   Output labels: {output_labels}")
        
        # Augment data
        print("\n3. Augmenting time series data...")
        augmented_data, augmented_labels = augment_time_series_data(
            input_data, output_labels, num_augmentations=4
        )
        
        # Reshape data
        print("\n4. Reshaping data...")
        reshaped_data, reshaped_labels = reshape_time_series_data_v8(
            augmented_data, augmented_labels, 
            segments_per_new_sample=10, 
            segment_length=4000
        )
        
        # Prepare train/test split
        print("\n5. Preparing train/test data...")
        X_train, X_valid, y_train, y_valid = prepare_train_test_data(
            reshaped_data, reshaped_labels, test_size=0.2, random_state=42
        )
        
        # Create comprehensive data dictionary
        data_dict = {
            'raw_data': all_data,
            'input_data': input_data,
            'output_labels': output_labels,
            'augmented_data': augmented_data,
            'augmented_labels': augmented_labels,
            'reshaped_data': reshaped_data,
            'reshaped_labels': reshaped_labels,
            'X_train': X_train,
            'X_valid': X_valid,
            'y_train': y_train,
            'y_valid': y_valid
        }
        
        print("✅ Data preparation completed successfully!")
        return data_dict
        
    except Exception as e:
        print(f"❌ Error in data preparation: {str(e)}")
        import traceback
        traceback.print_exc()
        return None

def analyze_models():
    """Analyze available model files"""
    print("\n🔍 MODEL ANALYSIS")
    print("="*60)
    
    model_files = [
        '1d_cnn_model.keras',
        'cnn_lstm_model.keras', 
        'lstm_model.keras',
        'resnet_model.keras'
    ]
    
    model_info = {}
    
    for model_file in model_files:
        if os.path.exists(model_file):
            size = os.path.getsize(model_file)
            size_mb = size / (1024 * 1024)
            
            model_info[model_file] = {
                'size_mb': size_mb,
                'exists': True,
                'path': os.path.abspath(model_file)
            }
            
            print(f"✅ {model_file}")
            print(f"   Size: {size_mb:.1f} MB")
            print(f"   Path: {os.path.abspath(model_file)}")
        else:
            model_info[model_file] = {'exists': False}
            print(f"❌ {model_file}: Not found")
    
    return model_info

def analyze_results():
    """Analyze results file"""
    print("\n📊 RESULTS ANALYSIS")
    print("="*60)
    
    results_file = 'model_comparison_results_20250628_001945.pkl'
    
    if os.path.exists(results_file):
        size = os.path.getsize(results_file)
        size_mb = size / (1024 * 1024)
        
        print(f"✅ {results_file}")
        print(f"   Size: {size_mb:.1f} MB")
        print(f"   Path: {os.path.abspath(results_file)}")
        print("   ⚠️  Contains custom classes (ResNetClassifier, TSNECallback)")
        print("   ⚠️  Requires compatible TensorFlow environment to load")
        
        return {
            'exists': True,
            'size_mb': size_mb,
            'path': os.path.abspath(results_file)
        }
    else:
        print(f"❌ {results_file}: Not found")
        return {'exists': False}

def create_loading_guide(data_dict, model_info):
    """Create comprehensive loading guide"""
    print("\n📖 COMPREHENSIVE LOADING GUIDE")
    print("="*60)
    
    print("🔧 ENVIRONMENT SETUP:")
    print("Due to NumPy/TensorFlow compatibility issues in current environment,")
    print("use a fresh Python environment with compatible versions:")
    print()
    print("conda create -n tf_env python=3.9")
    print("conda activate tf_env")
    print("pip install tensorflow==2.13.0 numpy==1.24.3 scikit-learn matplotlib scipy")
    print()
    
    print("📦 DATA LOADING (Already completed):")
    if data_dict:
        print("✅ Data is ready to use:")
        print(f"   - Training data: {data_dict['X_train'].shape}")
        print(f"   - Validation data: {data_dict['X_valid'].shape}")
        print(f"   - Training labels: {data_dict['y_train'].shape}")
        print(f"   - Validation labels: {data_dict['y_valid'].shape}")
        print(f"   - Number of classes: {len(np.unique(data_dict['y_train']))}")
        print(f"   - Data type: {data_dict['X_train'].dtype}")
        print(f"   - Label range: {np.min(data_dict['y_train'])} to {np.max(data_dict['y_train'])}")
    
    print("\n🤖 MODEL LOADING:")
    available_models = [f for f, info in model_info.items() if info['exists']]
    
    if available_models:
        print("Available models:")
        for model_file in available_models:
            model_name = model_file.replace('.keras', '')
            print(f"   - {model_name} ({model_info[model_file]['size_mb']:.1f} MB)")
        
        print("\nLoading code:")
        print("```python")
        print("import tensorflow as tf")
        print("import numpy as np")
        print()
        print("# Load models")
        print("models = {}")
        for model_file in available_models:
            model_name = model_file.replace('.keras', '')
            print(f"models['{model_name}'] = tf.keras.models.load_model('{model_file}')")
        
        print()
        print("# Load data (use the data variable from this script)")
        print("X_train = data['X_train']")
        print("X_valid = data['X_valid']")
        print("y_train = data['y_train']")
        print("y_valid = data['y_valid']")
        print()
        print("# Example: Evaluate a model")
        model_name = available_models[0].replace('.keras', '')
        print(f"loss, accuracy = models['{model_name}'].evaluate(X_valid, y_valid)")
        print(f"predictions = models['{model_name}'].predict(X_valid)")
        print("```")
    
    print("\n📊 RESULTS LOADING:")
    print("The pickle file contains model comparison results but requires")
    print("a compatible environment. To load safely:")
    print("```python")
    print("import pickle")
    print("try:")
    print("    with open('model_comparison_results_20250628_001945.pkl', 'rb') as f:")
    print("        results = pickle.load(f)")
    print("except Exception as e:")
    print("    print(f'Error loading results: {e}')")
    print("```")

def create_data_summary(data_dict):
    """Create detailed data summary"""
    if not data_dict:
        return
    
    print("\n📈 DETAILED DATA SUMMARY")
    print("="*60)
    
    # Training data analysis
    X_train = data_dict['X_train']
    y_train = data_dict['y_train']
    
    print(f"Training Data Shape: {X_train.shape}")
    print(f"  - Samples: {X_train.shape[0]}")
    print(f"  - Features per sample: {X_train.shape[1]} x {X_train.shape[2]} = {X_train.shape[1] * X_train.shape[2]}")
    print(f"  - Data type: {X_train.dtype}")
    print(f"  - Memory usage: {X_train.nbytes / (1024**2):.1f} MB")
    
    print(f"\nData Statistics:")
    print(f"  - Mean: {np.mean(X_train):.6f}")
    print(f"  - Std: {np.std(X_train):.6f}")
    print(f"  - Min: {np.min(X_train):.6f}")
    print(f"  - Max: {np.max(X_train):.6f}")
    
    print(f"\nLabel Information:")
    print(f"  - Shape: {y_train.shape}")
    print(f"  - Type: {y_train.dtype}")
    print(f"  - Classes: {len(np.unique(y_train))}")
    print(f"  - Range: {np.min(y_train)} to {np.max(y_train)}")
    print(f"  - Distribution: {np.bincount(y_train.astype(int))}")

def main():
    """Main function"""
    print("🚀 COMPLETE DATA AND MODEL LOADER")
    print("="*60)
    print("Loading all data and analyzing models/results...")
    
    # Load complete dataset
    data_dict = load_complete_dataset()
    
    # Analyze models
    model_info = analyze_models()
    
    # Analyze results
    results_info = analyze_results()
    
    # Create detailed summaries
    create_data_summary(data_dict)
    create_loading_guide(data_dict, model_info)
    
    print("\n" + "="*60)
    print("✅ COMPLETE ANALYSIS FINISHED")
    print("="*60)
    
    return {
        'data': data_dict,
        'model_info': model_info,
        'results_info': results_info
    }

if __name__ == "__main__":
    # Run complete analysis
    result = main()
    
    # Make variables available
    data = result['data']
    model_info = result['model_info']
    results_info = result['results_info']
    
    print("Available variables:")
    print("  - data: Complete processed dataset")
    print("  - model_info: Information about available models")
    print("  - results_info: Information about results file")
    print("\nData keys:", list(data.keys()) if data else "None")
