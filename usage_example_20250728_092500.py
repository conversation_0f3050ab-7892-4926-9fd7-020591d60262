# USAGE EXAMPLE
import numpy as np

# Load the processed data
data = np.load('augmented_reshaped_data_20250728_092500.npz')
reshaped_data = data['reshaped_data']      # Shape: (1232, 10, 4000)
reshaped_labels = data['reshaped_labels']  # Shape: (1232,)

print(f"Loaded {len(reshaped_labels)} samples")
print(f"Input shape per sample: {reshaped_data.shape[1:]}")
print(f"Number of classes: {len(np.unique(reshaped_labels))}")

# Ready for train/test split and model training
from sklearn.model_selection import train_test_split

X_train, X_valid, y_train, y_valid = train_test_split(
    reshaped_data, reshaped_labels, test_size=0.2, random_state=42
)

print(f"Training: {X_train.shape}")
print(f"Validation: {X_valid.shape}")
