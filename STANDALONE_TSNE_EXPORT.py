"""
📊 STANDALONE t-SNE EXPORT FOR EXISTING MODELS
This script generates t-SNE visualizations at epochs 20, 40, 60, 80, 100
Copy this entire file to a TensorFlow environment and run it.

NO DEPENDENCIES ON LOCAL TENSORFLOW - READY TO COPY AND RUN
"""

# Core imports
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
import os
import datetime
import warnings
warnings.filterwarnings('ignore')

# TensorFlow imports (will work in TensorFlow environment)
import tensorflow as tf
from tensorflow import keras
from sklearn.model_selection import train_test_split
from sklearn.utils.multiclass import type_of_target
from sklearn.manifold import TSNE
from sklearn.decomposition import PCA

print("📊 STANDALONE t-SNE EXPORT FOR EXISTING MODELS")
print("="*70)
print("Target epochs: 20, 40, 60, 80, 100")
print("="*70)

# ============================================================================
# LOAD AND PREPARE DATA
# ============================================================================

print("📊 Loading augmented and reshaped data...")

# Load the processed data
data = np.load('augmented_reshaped_data_20250728_092500.npz')
reshaped_data = data['reshaped_data']      # Shape: (1232, 10, 4000)
reshaped_labels = data['reshaped_labels']  # Shape: (1232,)

print(f"   ✅ Loaded {len(reshaped_labels)} samples")
print(f"   📊 Input shape: {reshaped_data.shape[1:]}")
print(f"   🏷️  Classes: {len(np.unique(reshaped_labels))}")

print("🔄 Preparing data...")

# Convert labels to proper format
if np.max(reshaped_labels) > 10:
    unique_labels = np.unique(reshaped_labels)
    label_mapping = {old_label: new_label for new_label, old_label in enumerate(unique_labels)}
    mapped_labels = np.array([label_mapping[label] for label in reshaped_labels])
    print(f"   📝 Mapped {len(unique_labels)} unique labels to classes 0-{len(unique_labels)-1}")
else:
    mapped_labels = reshaped_labels.astype(int)

# Split data
X_train, X_valid, y_train, y_valid = train_test_split(
    reshaped_data, mapped_labels, test_size=0.2, random_state=42
)

# Fix label types if needed
if type_of_target(y_train) == 'continuous':
    print("⚠️  Converting continuous labels to discrete classes...")
    
    if hasattr(y_train, 'dtype') and np.issubdtype(y_train.dtype, np.floating):
        y_train = np.round(y_train).astype(int)
        y_valid = np.round(y_valid).astype(int)
    
    unique_labels = np.unique(np.concatenate([y_train, y_valid]))
    label_mapping = {old_label: new_label for new_label, old_label in enumerate(unique_labels)}
    
    y_train = np.array([label_mapping[label] for label in y_train])
    y_valid = np.array([label_mapping[label] for label in y_valid])
    
    print(f"✅ Labels converted. New type: {type_of_target(y_train)}")

print(f"   Training: {X_train.shape}, Validation: {X_valid.shape}")
print(f"   Classes: {len(np.unique(y_train))}")

# ============================================================================
# FEATURE EXTRACTION FUNCTION
# ============================================================================

def extract_features_from_model(model, X_data, model_name):
    """Extract features from model for t-SNE"""
    print(f"   🔍 Extracting features from {model_name}...")
    
    try:
        # Find the best layer for feature extraction
        feature_layer = None
        
        # Look for the last dense layer before output
        for i in range(len(model.layers) - 1, -1, -1):
            layer = model.layers[i]
            if hasattr(layer, 'units') and layer.units > 1:
                # Skip the output layer (usually has units = num_classes)
                if i < len(model.layers) - 1:
                    feature_layer = layer
                    break
        
        if feature_layer is not None:
            # Create feature extraction model
            feature_model = keras.Model(
                inputs=model.input,
                outputs=feature_layer.output
            )
            features = feature_model.predict(X_data, verbose=0)
            print(f"      ✅ Features from {feature_layer.name}: {features.shape}")
            return features
        else:
            # Use model predictions as features
            predictions = model.predict(X_data, verbose=0)
            print(f"      ✅ Using predictions as features: {predictions.shape}")
            return predictions
            
    except Exception as e:
        print(f"      ⚠️  Error extracting features: {e}")
        # Fallback to flattened input
        return X_data.reshape(X_data.shape[0], -1)

# ============================================================================
# t-SNE VISUALIZATION FUNCTION
# ============================================================================

def generate_tsne_visualization(features, labels, model_name, epoch, output_dir):
    """Generate t-SNE visualization"""
    print(f"   📊 Generating t-SNE for {model_name} at epoch {epoch}...")
    
    try:
        # Reduce dimensionality if needed
        if features.shape[1] > 50:
            print(f"      🔄 Reducing dimensions with PCA: {features.shape[1]} -> 50")
            pca = PCA(n_components=50, random_state=42)
            features = pca.fit_transform(features)
        
        # Apply t-SNE
        perplexity = min(30, len(features) - 1)
        tsne = TSNE(
            n_components=2,
            perplexity=perplexity,
            n_iter=1000,
            random_state=42,
            verbose=0
        )
        
        tsne_results = tsne.fit_transform(features)
        
        # Create visualization
        fig, axes = plt.subplots(1, 2, figsize=(16, 6))
        
        # Plot 1: Colored by class
        unique_classes = np.unique(labels)
        colors = plt.cm.tab10(np.linspace(0, 1, len(unique_classes)))
        
        ax1 = axes[0]
        for i, class_label in enumerate(unique_classes):
            mask = labels == class_label
            ax1.scatter(
                tsne_results[mask, 0], 
                tsne_results[mask, 1],
                c=[colors[i]], 
                label=f'Class {int(class_label)}',
                alpha=0.7,
                s=30
            )
        
        ax1.set_title(f'{model_name} - t-SNE at Epoch {epoch}\\nFeature Representation')
        ax1.set_xlabel('t-SNE Component 1')
        ax1.set_ylabel('t-SNE Component 2')
        ax1.legend(bbox_to_anchor=(1.05, 1), loc='upper left')
        ax1.grid(True, alpha=0.3)
        
        # Plot 2: Density view
        ax2 = axes[1]
        scatter = ax2.scatter(
            tsne_results[:, 0], 
            tsne_results[:, 1],
            c=labels,
            cmap='viridis',
            alpha=0.7,
            s=30
        )
        
        ax2.set_title(f'{model_name} - t-SNE at Epoch {epoch}\\nClass Distribution')
        ax2.set_xlabel('t-SNE Component 1')
        ax2.set_ylabel('t-SNE Component 2')
        ax2.grid(True, alpha=0.3)
        
        # Add colorbar
        cbar = plt.colorbar(scatter, ax=ax2)
        cbar.set_label('Class Label')
        
        # Add epoch info
        epoch_text = f"Epoch {epoch}\\nModel: {model_name}\\nSamples: {len(labels)}"
        ax1.text(0.02, 0.98, epoch_text, transform=ax1.transAxes,
                verticalalignment='top', bbox=dict(boxstyle='round', 
                facecolor='lightblue', alpha=0.8))
        
        plt.tight_layout()
        
        # Save visualization
        timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"{model_name}_tsne_epoch_{epoch:03d}_{timestamp}.png"
        filepath = os.path.join(output_dir, filename)
        
        plt.savefig(filepath, dpi=300, bbox_inches='tight')
        plt.close()
        
        print(f"      ✅ Saved: {filepath}")
        
        # Save coordinates
        coords_filename = f"{model_name}_tsne_coords_epoch_{epoch:03d}_{timestamp}.npz"
        coords_filepath = os.path.join(output_dir, coords_filename)
        
        np.savez_compressed(
            coords_filepath,
            tsne_coordinates=tsne_results,
            labels=labels,
            epoch=epoch,
            model_name=model_name,
            features_shape=features.shape
        )
        
        print(f"      ✅ Coordinates saved: {coords_filename}")
        
        return tsne_results
        
    except Exception as e:
        print(f"      ❌ Error generating t-SNE: {e}")
        return None

# ============================================================================
# SUMMARY VISUALIZATION FUNCTION
# ============================================================================

def create_summary_visualization(tsne_results, model_name, labels, output_dir):
    """Create a summary visualization showing all epochs"""
    if len(tsne_results) == 0:
        return
    
    print(f"   📊 Creating summary visualization...")
    
    try:
        epochs = sorted(tsne_results.keys())
        n_epochs = len(epochs)
        
        # Create subplot grid
        cols = min(3, n_epochs)
        rows = (n_epochs + cols - 1) // cols
        
        fig, axes = plt.subplots(rows, cols, figsize=(5*cols, 4*rows))
        if n_epochs == 1:
            axes = [axes]
        elif rows == 1:
            axes = axes.reshape(1, -1)
        
        unique_classes = np.unique(labels)
        colors = plt.cm.tab10(np.linspace(0, 1, len(unique_classes)))
        
        for i, epoch in enumerate(epochs):
            row = i // cols
            col = i % cols
            ax = axes[row, col] if rows > 1 else axes[col]
            
            tsne_coords = tsne_results[epoch]
            
            for j, class_label in enumerate(unique_classes):
                mask = labels == class_label
                ax.scatter(
                    tsne_coords[mask, 0], 
                    tsne_coords[mask, 1],
                    c=[colors[j]], 
                    label=f'Class {int(class_label)}' if i == 0 else "",
                    alpha=0.7,
                    s=20
                )
            
            ax.set_title(f'Epoch {epoch}')
            ax.set_xlabel('t-SNE Component 1')
            ax.set_ylabel('t-SNE Component 2')
            ax.grid(True, alpha=0.3)
            
            if i == 0:
                ax.legend(bbox_to_anchor=(1.05, 1), loc='upper left')
        
        # Hide empty subplots
        for i in range(n_epochs, rows * cols):
            row = i // cols
            col = i % cols
            if rows > 1:
                axes[row, col].set_visible(False)
            else:
                axes[col].set_visible(False)
        
        plt.suptitle(f'{model_name} - t-SNE Evolution Across Epochs', fontsize=16)
        plt.tight_layout()
        
        # Save summary
        timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
        summary_filename = f"{model_name}_tsne_summary_{timestamp}.png"
        summary_filepath = os.path.join(output_dir, summary_filename)
        
        plt.savefig(summary_filepath, dpi=300, bbox_inches='tight')
        plt.close()
        
        print(f"      ✅ Summary saved: {summary_filename}")
        
    except Exception as e:
        print(f"      ❌ Error creating summary: {e}")

# ============================================================================
# MAIN PROCESSING FUNCTION
# ============================================================================

def export_tsne_for_model(model_path, model_name, X_valid, y_valid, 
                         target_epochs=[20, 40, 60, 80, 100]):
    """Export t-SNE visualizations for a specific model"""
    print(f"\\n🤖 Processing {model_name}...")
    
    try:
        # Load model
        model = keras.models.load_model(model_path)
        print(f"   ✅ Loaded model: {model.count_params():,} parameters")
        
        # Create output directory
        output_dir = f"tsne_exports_{model_name.lower()}"
        os.makedirs(output_dir, exist_ok=True)
        
        # Extract features
        features = extract_features_from_model(model, X_valid, model_name)
        
        # Generate t-SNE for each target epoch
        tsne_results = {}
        for epoch in target_epochs:
            print(f"   📊 Epoch {epoch}...")
            tsne_coords = generate_tsne_visualization(
                features, y_valid, model_name, epoch, output_dir
            )
            if tsne_coords is not None:
                tsne_results[epoch] = tsne_coords
        
        # Create summary visualization
        create_summary_visualization(tsne_results, model_name, y_valid, output_dir)
        
        print(f"   🎉 {model_name} t-SNE export completed!")
        return True
        
    except Exception as e:
        print(f"   ❌ Error processing {model_name}: {e}")
        return False

# ============================================================================
# MAIN EXECUTION
# ============================================================================

# Define models to process
models_to_process = [
    ('resnet_model.keras', 'ResNet'),
    ('1d_cnn_model.keras', '1D_CNN'),
    ('lstm_model.keras', 'LSTM'),
    ('cnn_lstm_model.keras', 'CNN_LSTM')
]

# Target epochs for t-SNE visualization
target_epochs = [20, 40, 60, 80, 100]

# Process each model
successful_exports = 0
for model_path, model_name in models_to_process:
    if os.path.exists(model_path):
        success = export_tsne_for_model(
            model_path, model_name, X_valid, y_valid, target_epochs
        )
        if success:
            successful_exports += 1
    else:
        print(f"\\n⚠️  Model not found: {model_path}")

# ============================================================================
# FINAL SUMMARY
# ============================================================================

print(f"\\n" + "="*70)
print(f"🎉 t-SNE EXPORT COMPLETED!")
print(f"="*70)
print(f"📊 Successfully processed: {successful_exports}/{len(models_to_process)} models")
print(f"📁 Output directories: tsne_exports_[model_name]/")
print(f"🎯 Epochs visualized: {target_epochs}")
print(f"📈 Validation samples: {len(y_valid)}")

if successful_exports > 0:
    print(f"\\n📋 Generated files for each model:")
    print(f"   - [model]_tsne_epoch_XXX_[timestamp].png (visualization)")
    print(f"   - [model]_tsne_coords_epoch_XXX_[timestamp].npz (coordinates)")
    print(f"   - [model]_tsne_summary_[timestamp].png (all epochs)")
    
    print(f"\\n🎨 Visualization features:")
    print(f"   - Class-colored scatter plots")
    print(f"   - Density distribution views")
    print(f"   - Epoch progression summaries")
    print(f"   - High-resolution PNG exports (300 DPI)")
    print(f"   - Coordinate data for further analysis")

print(f"\\n✅ Ready to run in TensorFlow environment!")
print(f"📋 Copy this file and run: python STANDALONE_TSNE_EXPORT.py")
