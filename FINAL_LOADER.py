"""
🚀 FINAL COMPLETE LOADER
This script loads all data and provides ready-to-use code for models
"""
import os
import numpy as np
from load_data import (
    load_mat_data, 
    prepare_input_data, 
    augment_time_series_data,
    reshape_time_series_data_v8,
    prepare_train_test_data
)

def load_everything():
    """Load all data and prepare everything"""
    print("🚀 FINAL COMPLETE LOADER")
    print("="*60)
    
    # Load and process data
    print("📊 Loading and processing data...")
    all_data = load_mat_data('data')
    input_data, output_labels = prepare_input_data(all_data)
    augmented_data, augmented_labels = augment_time_series_data(input_data, output_labels, num_augmentations=4)
    reshaped_data, reshaped_labels = reshape_time_series_data_v8(augmented_data, augmented_labels, segments_per_new_sample=10, segment_length=4000)
    X_train, X_valid, y_train, y_valid = prepare_train_test_data(reshaped_data, reshaped_labels, test_size=0.2, random_state=42)
    
    # Create complete data dictionary
    data = {
        'raw_data': all_data,
        'input_data': input_data,
        'output_labels': output_labels,
        'augmented_data': augmented_data,
        'augmented_labels': augmented_labels,
        'reshaped_data': reshaped_data,
        'reshaped_labels': reshaped_labels,
        'X_train': X_train,
        'X_valid': X_valid,
        'y_train': y_train,
        'y_valid': y_valid
    }
    
    print("✅ DATA LOADED SUCCESSFULLY!")
    
    # Check model files
    print("\n🤖 CHECKING MODEL FILES...")
    model_files = {
        '1d_cnn_model': '1d_cnn_model.keras',
        'cnn_lstm_model': 'cnn_lstm_model.keras',
        'lstm_model': 'lstm_model.keras',
        'resnet_model': 'resnet_model.keras'
    }
    
    available_models = {}
    for name, file in model_files.items():
        if os.path.exists(file):
            size_mb = os.path.getsize(file) / (1024*1024)
            available_models[name] = {
                'file': file,
                'size_mb': size_mb,
                'path': os.path.abspath(file)
            }
            print(f"✅ {name}: {size_mb:.1f} MB")
        else:
            print(f"❌ {name}: Not found")
    
    # Check results file
    print("\n📈 CHECKING RESULTS FILE...")
    results_file = 'model_comparison_results_20250628_001945.pkl'
    results_info = None
    if os.path.exists(results_file):
        size_mb = os.path.getsize(results_file) / (1024*1024)
        results_info = {
            'file': results_file,
            'size_mb': size_mb,
            'path': os.path.abspath(results_file)
        }
        print(f"✅ Results file: {size_mb:.1f} MB")
    else:
        print("❌ Results file not found")
    
    return data, available_models, results_info

def print_summary(data, available_models, results_info):
    """Print comprehensive summary"""
    print("\n" + "="*60)
    print("📋 COMPLETE SUMMARY")
    print("="*60)
    
    # Data summary
    print("📊 DATA READY:")
    print(f"   ✅ Training samples: {data['X_train'].shape[0]}")
    print(f"   ✅ Validation samples: {data['X_valid'].shape[0]}")
    print(f"   ✅ Input shape: {data['X_train'].shape[1:]}")
    print(f"   ✅ Number of classes: {len(np.unique(data['y_train']))}")
    print(f"   ✅ Data type: {data['X_train'].dtype}")
    print(f"   ✅ Label range: {np.min(data['y_train'])} to {np.max(data['y_train'])}")
    
    # Model summary
    print(f"\n🤖 MODELS AVAILABLE: {len(available_models)}")
    total_size = 0
    for name, info in available_models.items():
        print(f"   ✅ {name}: {info['size_mb']:.1f} MB")
        total_size += info['size_mb']
    print(f"   📦 Total size: {total_size:.1f} MB")
    
    # Results summary
    print(f"\n📈 RESULTS FILE:")
    if results_info:
        print(f"   ✅ Available: {results_info['size_mb']:.1f} MB")
    else:
        print("   ❌ Not available")

def print_usage_instructions(available_models):
    """Print detailed usage instructions"""
    print("\n" + "="*60)
    print("📝 USAGE INSTRUCTIONS")
    print("="*60)
    
    print("🔧 STEP 1: Load Models (copy and paste this code)")
    print("-" * 50)
    print("import tensorflow as tf")
    print("import numpy as np")
    print()
    print("# Load all models")
    print("models = {}")
    
    for name, info in available_models.items():
        print(f"models['{name}'] = tf.keras.models.load_model('{info['file']}')")
    
    print()
    print("# Verify models loaded")
    print("for name, model in models.items():")
    print("    print(f'{name}: {model.count_params():,} parameters')")
    
    print("\n🔍 STEP 2: Use Data (already loaded)")
    print("-" * 50)
    print("# Data is already available in 'data' variable:")
    print("X_train = data['X_train']  # Training data")
    print("X_valid = data['X_valid']  # Validation data")
    print("y_train = data['y_train']  # Training labels")
    print("y_valid = data['y_valid']  # Validation labels")
    
    print("\n🎯 STEP 3: Evaluate Models")
    print("-" * 50)
    print("# Evaluate all models")
    print("for name, model in models.items():")
    print("    loss, accuracy = model.evaluate(X_valid, y_valid, verbose=0)")
    print("    print(f'{name}: Loss={loss:.4f}, Accuracy={accuracy:.4f}')")
    
    print("\n🔮 STEP 4: Make Predictions")
    print("-" * 50)
    print("# Make predictions with a specific model")
    if available_models:
        first_model = list(available_models.keys())[0]
        print(f"predictions = models['{first_model}'].predict(X_valid)")
        print("predicted_classes = np.argmax(predictions, axis=1)")
        print("print(f'Predicted classes: {predicted_classes[:10]}')")
    
    print("\n📊 STEP 5: Load Results (optional)")
    print("-" * 50)
    print("import pickle")
    print("try:")
    print("    with open('model_comparison_results_20250628_001945.pkl', 'rb') as f:")
    print("        results = pickle.load(f)")
    print("    print('Results loaded successfully!')")
    print("except Exception as e:")
    print("    print(f'Error loading results: {e}')")

def main():
    """Main execution"""
    # Load everything
    data, available_models, results_info = load_everything()
    
    # Print summary
    print_summary(data, available_models, results_info)
    
    # Print usage instructions
    print_usage_instructions(available_models)
    
    print("\n" + "="*60)
    print("✅ EVERYTHING IS READY!")
    print("="*60)
    print("Variables available:")
    print("  - data: Complete dataset dictionary")
    print("  - available_models: Model information")
    print("  - results_info: Results file information")
    
    return data, available_models, results_info

if __name__ == "__main__":
    # Execute main function
    data, available_models, results_info = main()
    
    print(f"\n🎉 SUCCESS! You now have:")
    print(f"   📊 Data loaded: {data['X_train'].shape[0]} training samples")
    print(f"   🤖 Models available: {len(available_models)}")
    print(f"   📈 Results file: {'Available' if results_info else 'Not found'}")
    print(f"\n💡 Copy the code above to load and use the models!")
