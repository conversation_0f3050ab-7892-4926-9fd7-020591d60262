"""
🔮 FINAL PREDICTION ANALYSIS - Data Ready + Complete Prediction Code
This script loads the augmented data and provides everything needed for predictions
"""
import numpy as np
import pandas as pd
from datetime import datetime
import os

def load_augmented_data():
    """Load the properly augmented data"""
    print("📊 Loading augmented data for predictions...")
    
    try:
        # Load using the fixed data loader
        from fixed_data_loader import load_complete_dataset_with_augmentation
        
        print("   Loading complete augmented dataset...")
        data_dict = load_complete_dataset_with_augmentation()
        
        if data_dict:
            print("   ✅ SUCCESS: Augmented data loaded!")
            print(f"   Original samples: {data_dict['input_data'].shape[0]}")
            print(f"   Augmented samples: {data_dict['augmented_data'].shape[0]}")
            print(f"   Augmentation factor: {data_dict['augmented_data'].shape[0] / data_dict['input_data'].shape[0]:.1f}x")
            print(f"   Training samples: {data_dict['X_train'].shape[0]}")
            print(f"   Validation samples: {data_dict['X_valid'].shape[0]}")
            return data_dict
        else:
            print("   ❌ Failed to load augmented data")
            return None
            
    except Exception as e:
        print(f"   ❌ Error loading augmented data: {e}")
        return None

def analyze_validation_data(data_dict):
    """Analyze the validation data that will be used for predictions"""
    print("\n📈 VALIDATION DATA ANALYSIS")
    print("="*60)
    
    X_valid = data_dict['X_valid']
    y_valid = data_dict['y_valid']
    
    print(f"📊 Dataset characteristics:")
    print(f"   Validation samples: {X_valid.shape[0]}")
    print(f"   Input shape per sample: {X_valid.shape[1:]}")
    print(f"   Number of classes: {len(np.unique(y_valid))}")
    print(f"   Label range: {np.min(y_valid)} to {np.max(y_valid)}")
    
    print(f"\n📈 Data statistics:")
    print(f"   Mean: {np.mean(X_valid):.6f}")
    print(f"   Std: {np.std(X_valid):.6f}")
    print(f"   Min: {np.min(X_valid):.6f}")
    print(f"   Max: {np.max(X_valid):.6f}")
    
    print(f"\n🏷️  Label distribution:")
    unique_labels, counts = np.unique(y_valid, return_counts=True)
    for label, count in zip(unique_labels, counts):
        print(f"   Class {label}: {count} samples ({count/len(y_valid)*100:.1f}%)")
    
    print(f"\n📋 Sample data preview:")
    print(f"   First 10 labels: {y_valid[:10]}")
    print(f"   Last 10 labels: {y_valid[-10:]}")
    
    # Check for class balance
    class_balance = np.std(counts) / np.mean(counts)
    print(f"\n⚖️  Class balance (lower is better): {class_balance:.3f}")
    if class_balance < 0.1:
        print("   ✅ Well balanced dataset")
    elif class_balance < 0.3:
        print("   ⚠️  Moderately balanced dataset")
    else:
        print("   ❌ Imbalanced dataset - consider this in evaluation")

def check_model_files():
    """Check available model files and their properties"""
    print("\n🤖 MODEL FILES ANALYSIS")
    print("="*60)
    
    model_files = {
        '1d_cnn_model': '1d_cnn_model.keras',
        'cnn_lstm_model': 'cnn_lstm_model.keras',
        'lstm_model': 'lstm_model.keras',
        'resnet_model': 'resnet_model.keras'
    }
    
    available_models = {}
    total_size = 0
    
    for name, file_path in model_files.items():
        if os.path.exists(file_path):
            size_mb = os.path.getsize(file_path) / (1024*1024)
            total_size += size_mb
            available_models[name] = {
                'file': file_path,
                'size_mb': size_mb,
                'path': os.path.abspath(file_path)
            }
            print(f"   ✅ {name}: {size_mb:.1f} MB")
        else:
            print(f"   ❌ {name}: NOT FOUND")
    
    print(f"\n📦 Summary:")
    print(f"   Available models: {len(available_models)}/4")
    print(f"   Total size: {total_size:.1f} MB")
    
    return available_models

def create_prediction_code(data_dict, available_models):
    """Create the complete prediction code"""
    print("\n📝 Creating complete prediction code...")
    
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    
    prediction_code = f'''"""
🔮 COMPLETE PREDICTION CODE - READY TO RUN
Generated: {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}

This code uses the properly augmented dataset with {data_dict['X_valid'].shape[0]} validation samples
"""
import tensorflow as tf
import numpy as np
import pandas as pd
from datetime import datetime

print("🔮 MAKING PREDICTIONS WITH ALL 4 MODELS")
print("="*60)
print("Using properly augmented dataset:")
print(f"  Validation samples: {data_dict['X_valid'].shape[0]}")
print(f"  Input shape: {data_dict['X_valid'].shape[1:]}")
print(f"  Classes: {len(np.unique(data_dict['y_valid']))} (range: {np.min(data_dict['y_valid'])}-{np.max(data_dict['y_valid'])})")

# STEP 1: Load the augmented data
print("\\n📊 Loading augmented data...")

# Load the pre-processed augmented data
X_train = np.array({data_dict['X_train'].tolist()})
X_valid = np.array({data_dict['X_valid'].tolist()})
y_train = np.array({data_dict['y_train'].tolist()})
y_valid = np.array({data_dict['y_valid'].tolist()})

print(f"✅ Data loaded:")
print(f"  X_train: {{X_train.shape}}")
print(f"  X_valid: {{X_valid.shape}}")
print(f"  y_train: {{y_train.shape}}")
print(f"  y_valid: {{y_valid.shape}}")

# STEP 2: Load all 4 models
print("\\n🤖 Loading models...")
models = {{}}

model_files = {{'''

    for name, info in available_models.items():
        prediction_code += f'''
    '{name}': '{info['file']}','''
    
    prediction_code += f'''
}}

for name, file_path in model_files.items():
    try:
        models[name] = tf.keras.models.load_model(file_path)
        print(f"   ✅ {{name}}: {{models[name].count_params():,}} parameters")
        print(f"      Input: {{models[name].input_shape}}")
        print(f"      Output: {{models[name].output_shape}}")
    except Exception as e:
        print(f"   ❌ {{name}}: {{e}}")

print(f"\\n📊 Successfully loaded {{len(models)}}/{len(available_models)} models")

# STEP 3: Make predictions
print("\\n🔮 Making predictions...")
predictions_results = {{}}

for model_name, model in models.items():
    print(f"\\n   Predicting with {{model_name}}...")
    
    try:
        # Make predictions
        raw_predictions = model.predict(X_valid, verbose=0)
        
        # Convert to class predictions
        if len(raw_predictions.shape) > 1 and raw_predictions.shape[1] > 1:
            # Multi-class classification
            predicted_classes = np.argmax(raw_predictions, axis=1)
            prediction_probabilities = raw_predictions
            max_probs = np.max(raw_predictions, axis=1)
        else:
            # Single output
            if len(raw_predictions.shape) > 1:
                raw_predictions = raw_predictions.flatten()
            predicted_classes = np.round(raw_predictions).astype(int)
            predicted_classes = np.clip(predicted_classes, 0, {len(np.unique(data_dict['y_valid']))-1})
            prediction_probabilities = raw_predictions
            max_probs = np.abs(raw_predictions)
        
        # Calculate metrics
        accuracy = np.mean(predicted_classes == y_valid)
        avg_confidence = np.mean(max_probs)
        
        # Store results
        predictions_results[model_name] = {{
            'predicted_classes': predicted_classes,
            'raw_predictions': raw_predictions,
            'probabilities': prediction_probabilities,
            'accuracy': accuracy,
            'avg_confidence': avg_confidence,
            'max_probs': max_probs
        }}
        
        print(f"      ✅ Accuracy: {{accuracy:.4f}} ({{accuracy*100:.2f}}%)")
        print(f"      📊 Avg confidence: {{avg_confidence:.4f}}")
        print(f"      🎯 Sample predictions: {{predicted_classes[:10]}}")
        print(f"      🏷️  Actual labels:      {{y_valid[:10]}}")
        
    except Exception as e:
        print(f"      ❌ Error: {{e}}")

# STEP 4: Analysis and comparison
print("\\n📊 MODEL COMPARISON")
print("="*60)

# Create comparison DataFrame
comparison_data = {{
    'sample_id': range(len(y_valid)),
    'actual': y_valid
}}

for model_name, results in predictions_results.items():
    if 'predicted_classes' in results:
        comparison_data[f'{{model_name}}_pred'] = results['predicted_classes']
        comparison_data[f'{{model_name}}_correct'] = (results['predicted_classes'] == y_valid).astype(int)
        comparison_data[f'{{model_name}}_confidence'] = results['max_probs']

comparison_df = pd.DataFrame(comparison_data)

# Print accuracy summary
print("🎯 ACCURACY SUMMARY:")
accuracies = []
for model_name, results in predictions_results.items():
    if 'accuracy' in results:
        acc = results['accuracy']
        conf = results.get('avg_confidence', 0)
        accuracies.append((model_name, acc, conf))
        print(f"   {{model_name:15s}}: {{acc:.4f}} ({{acc*100:.2f}}%) | Confidence: {{conf:.4f}}")

# Find best model
if accuracies:
    best_model = max(accuracies, key=lambda x: x[1])
    print(f"\\n🏆 BEST MODEL: {{best_model[0]}}")
    print(f"   Accuracy: {{best_model[1]:.4f}} ({{best_model[1]*100:.2f}}%)")
    print(f"   Confidence: {{best_model[2]:.4f}}")

# Per-class analysis
print("\\n📈 PER-CLASS ANALYSIS:")
for model_name, results in predictions_results.items():
    if 'predicted_classes' in results:
        print(f"\\n{{model_name}}:")
        y_pred = results['predicted_classes']
        
        for class_id in np.unique(y_valid):
            mask = y_valid == class_id
            if np.sum(mask) > 0:
                class_acc = np.mean(y_pred[mask] == class_id)
                count = np.sum(mask)
                print(f"  Class {{class_id}}: {{class_acc:.4f}} ({{count:2d}} samples)")

# Save results
print("\\n💾 Saving results...")
timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

# Save predictions
predictions_file = f"final_predictions_{{timestamp}}.csv"
comparison_df.to_csv(predictions_file, index=False)
print(f"   ✅ Predictions saved: {{predictions_file}}")

# Save summary
summary_file = f"prediction_summary_{{timestamp}}.txt"
with open(summary_file, 'w') as f:
    f.write("FINAL PREDICTION RESULTS\\n")
    f.write("="*50 + "\\n")
    f.write(f"Generated: {{datetime.now()}}\\n\\n")
    
    f.write("DATASET INFO:\\n")
    f.write(f"Validation samples: {{len(y_valid)}}\\n")
    f.write(f"Classes: {{len(np.unique(y_valid))}}\\n")
    f.write(f"Input shape: {{X_valid.shape}}\\n\\n")
    
    f.write("MODEL PERFORMANCE:\\n")
    for model_name, acc, conf in accuracies:
        f.write(f"{{model_name}}: {{acc:.4f}} ({{acc*100:.2f}}%) | Confidence: {{conf:.4f}}\\n")
    
    if accuracies:
        f.write(f"\\nBEST MODEL: {{best_model[0]}} ({{best_model[1]:.4f}})\\n")

print(f"   ✅ Summary saved: {{summary_file}}")

print("\\n🎉 PREDICTION COMPLETE!")
print("="*60)
print(f"📊 Processed {{len(y_valid)}} validation samples")
print(f"🤖 Used {{len(models)}} models")
print(f"📄 Files created: {{predictions_file}}, {{summary_file}}")

if accuracies:
    print(f"\\n🏆 FINAL RESULTS:")
    for model_name, acc, conf in sorted(accuracies, key=lambda x: x[1], reverse=True):
        print(f"   {{model_name:15s}}: {{acc:.4f}} ({{acc*100:.2f}}%) | Confidence: {{conf:.4f}}")

print(f"\\n📋 Available variables:")
print(f"   - models: Dictionary of loaded models")
print(f"   - predictions_results: All prediction results")
print(f"   - comparison_df: DataFrame with all predictions")
print(f"   - X_valid, y_valid: Validation data and labels")
'''
    
    # Save the prediction code
    code_file = f"READY_TO_RUN_PREDICTIONS_{timestamp}.py"
    with open(code_file, 'w', encoding='utf-8') as f:
        f.write(prediction_code)
    
    print(f"   ✅ Complete prediction code saved: {code_file}")
    return code_file

def create_data_export(data_dict):
    """Export the augmented data for use in TensorFlow environment"""
    print("\n💾 Exporting augmented data...")
    
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    
    # Export as NPZ
    data_file = f"augmented_data_{timestamp}.npz"
    np.savez_compressed(
        data_file,
        X_train=data_dict['X_train'],
        X_valid=data_dict['X_valid'],
        y_train=data_dict['y_train'],
        y_valid=data_dict['y_valid'],
        original_shape=data_dict['input_data'].shape,
        augmented_shape=data_dict['augmented_data'].shape
    )
    
    print(f"   ✅ Augmented data exported: {data_file}")
    
    # Create loading instructions
    instructions = f'''# LOADING INSTRUCTIONS FOR AUGMENTED DATA

## Load the augmented data:
```python
import numpy as np

# Load augmented data
data = np.load('{data_file}')
X_train = data['X_train']  # Shape: {data_dict['X_train'].shape}
X_valid = data['X_valid']  # Shape: {data_dict['X_valid'].shape}
y_train = data['y_train']  # Shape: {data_dict['y_train'].shape}
y_valid = data['y_valid']  # Shape: {data_dict['y_valid'].shape}

print(f"Loaded augmented dataset:")
print(f"  Training: {{X_train.shape}}")
print(f"  Validation: {{X_valid.shape}}")
print(f"  Classes: {{len(np.unique(y_train))}}")
```

## Dataset Information:
- Original samples: {data_dict['input_data'].shape[0]}
- Augmented samples: {data_dict['augmented_data'].shape[0]}
- Augmentation factor: {data_dict['augmented_data'].shape[0] / data_dict['input_data'].shape[0]:.1f}x
- Training samples: {data_dict['X_train'].shape[0]}
- Validation samples: {data_dict['X_valid'].shape[0]}
- Classes: {len(np.unique(data_dict['y_train']))}
'''
    
    with open(f"data_loading_instructions_{timestamp}.md", 'w') as f:
        f.write(instructions)
    
    return data_file

def main():
    """Main analysis function"""
    print("🔮 FINAL PREDICTION ANALYSIS")
    print("="*60)
    
    # Load augmented data
    data_dict = load_augmented_data()
    if not data_dict:
        print("❌ Cannot proceed without data")
        return None
    
    # Analyze validation data
    analyze_validation_data(data_dict)
    
    # Check model files
    available_models = check_model_files()
    
    # Create prediction code
    code_file = create_prediction_code(data_dict, available_models)
    
    # Export data
    data_file = create_data_export(data_dict)
    
    print("\n" + "="*60)
    print("✅ PREDICTION ANALYSIS COMPLETE!")
    print("="*60)
    print(f"📊 Augmented validation data: {data_dict['X_valid'].shape[0]} samples")
    print(f"🤖 Available models: {len(available_models)}/4")
    print(f"📝 Prediction code: {code_file}")
    print(f"💾 Data export: {data_file}")
    
    print(f"\n🚀 NEXT STEPS:")
    print(f"1. Copy {code_file} to TensorFlow environment")
    print(f"2. Run the prediction code")
    print(f"3. Get classification results for all models")
    
    return {
        'data': data_dict,
        'models': available_models,
        'code_file': code_file,
        'data_file': data_file
    }

if __name__ == "__main__":
    result = main()
    
    if result:
        data = result['data']
        print(f"\n📋 Ready for predictions:")
        print(f"   Validation samples: {data['X_valid'].shape[0]}")
        print(f"   Input shape: {data['X_valid'].shape[1:]}")
        print(f"   Classes: {len(np.unique(data['y_valid']))}")
        print(f"   Models available: {len(result['models'])}")
        print(f"\n💡 Run the generated prediction code to classify all {data['X_valid'].shape[0]} samples!")
