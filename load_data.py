import os
import numpy as np
import matplotlib.pyplot as plt
import random
from scipy.io import loadmat
from sklearn.model_selection import train_test_split
from sklearn.utils.multiclass import type_of_target

def load_mat_data(directory='data'):
    """Load all .mat files from directory"""
    all_data = {}
    
    # Iterate over all files in the directory
    for filename in os.listdir(directory):
        if filename.endswith('.mat'):
            filepath = os.path.join(directory, filename)
            # Load the .mat file and add its contents to the dictionary
            mat_data = loadmat(filepath)
            
            # Use filename (without extension) as key for the data
            key = os.path.splitext(filename)[0]
            all_data[key] = mat_data['acceleration']
    
    return all_data

def prepare_input_data(all_data):
    """Prepare input data and labels"""
    keys_to_stack = [f'NamO{i}' for i in range(11)]
    input_data = np.stack([all_data[key] for key in keys_to_stack], axis=0)
    
    # Create the corresponding labels
    output_labels = np.linspace(0, 28, 11)
    
    # Truncate to 40000 samples
    input_data = input_data[:, :, :40000]
    
    print(f"Input data shape: {input_data.shape}")
    print(f"Output labels shape: {output_labels.shape}")
    
    return input_data, output_labels

def augment_time_series_data(input_data, labels, num_augmentations=4):
    """Augment time series data"""
    augmented_data = []
    augmented_labels = []

    num_samples, num_channels, sequence_length = input_data.shape

    for i in range(num_samples):
        for _ in range(num_augmentations):
            # Choose a random augmentation technique
            augmentation_type = random.choice(['noise', 'reverse', 'crop_pad'])

            if augmentation_type == 'noise':
                # Add random noise
                noise = np.random.normal(0, 0.001, input_data[i].shape)
                augmented_sample = input_data[i] + noise

            elif augmentation_type == 'reverse':
                # Reverse the sequence
                augmented_sample = np.flip(input_data[i], axis=-1)

            elif augmentation_type == 'crop_pad':
                # Crop and pad the sequence
                crop_size = random.randint(0, sequence_length // 100)
                padded_sample = np.pad(input_data[i], ((0, 0), (crop_size, 0)), mode='constant', constant_values=0)
                augmented_sample = padded_sample[:, :-crop_size]

            augmented_data.append(augmented_sample)
            augmented_labels.append(labels[i])

    # Convert to numpy arrays
    augmented_data = np.array(augmented_data)
    augmented_labels = np.array(augmented_labels)
    
    print(f"Augmented data shape: {augmented_data.shape}")
    print(f"Augmented labels shape: {augmented_labels.shape}")

    return augmented_data, augmented_labels

def reshape_time_series_data_v8(input_data, label_data, segments_per_new_sample, segment_length):
    """Reshape time series data and corresponding labels into a specified shape"""
    num_samples_original, num_channels, length_original = input_data.shape

    # Validate the feasibility of reshaping
    if length_original % segment_length != 0:
        raise ValueError("Segment length must evenly divide the original length.")

    total_segments_per_original_sample = (length_original // segment_length) * num_channels
    num_samples_new = (num_samples_original * total_segments_per_original_sample) // segments_per_new_sample

    # Validate if reshaping is possible
    if (num_samples_original * total_segments_per_original_sample) % segments_per_new_sample != 0:
        raise ValueError("Reshaping not possible with the given dimensions.")

    # Initialize reshaped data and labels
    new_shape = (num_samples_new, segments_per_new_sample, segment_length)
    reshaped_data = np.zeros(new_shape)
    reshaped_labels = np.zeros(num_samples_new)

    # Reshape the data and labels
    count = 0
    for i in range(num_samples_original):
        segment_count = 0
        for j in range(num_channels):
            for k in range(length_original // segment_length):
                start_idx = k * segment_length
                end_idx = start_idx + segment_length
                reshaped_data[count, segment_count % segments_per_new_sample, :] = input_data[i, j, start_idx:end_idx]
                if (segment_count + 1) % segments_per_new_sample == 0:
                    reshaped_labels[count] = label_data[i]
                    count += 1
                segment_count += 1

    return reshaped_data, reshaped_labels

def prepare_train_test_data(reshaped_data, reshaped_labels, test_size=0.2, random_state=42):
    """Prepare training and testing data"""
    # Split the data into training and validation sets
    XXX_train_reshaped, XXX_valid_reshaped, y_train, y_valid = train_test_split(
        reshaped_data, reshaped_labels, test_size=test_size, random_state=random_state
    )

    # Reshape data
    XXX_train = XXX_train_reshaped.reshape(XXX_train_reshaped.shape[0], 10, 4000)
    XXX_valid = XXX_valid_reshaped.reshape(XXX_valid_reshaped.shape[0], 10, 4000)

    print(f"Training data shape: {XXX_train.shape}")
    print(f"Training labels shape: {y_train.shape}")
    print(f"Validation data shape: {XXX_valid.shape}")
    print(f"Validation labels shape: {y_valid.shape}")
    print(f"Label type: {type_of_target(y_train)}")
    print(f"Sample labels: {y_train[:10]}")

    # Convert labels to proper format if needed
    if type_of_target(y_train) == 'continuous':
        print("⚠️  Converting continuous labels to discrete classes...")
        
        # Round to nearest integer if floating point
        if hasattr(y_train, 'dtype') and np.issubdtype(y_train.dtype, np.floating):
            y_train = np.round(y_train).astype(int)
            y_valid = np.round(y_valid).astype(int)
        
        # Ensure labels start from 0
        unique_labels = np.unique(np.concatenate([y_train, y_valid]))
        label_mapping = {old_label: new_label for new_label, old_label in enumerate(unique_labels)}
        
        y_train = np.array([label_mapping[label] for label in y_train])
        y_valid = np.array([label_mapping[label] for label in y_valid])
        
        print(f"✅ Labels converted. New type: {type_of_target(y_train)}")

    print(f"Number of classes: {len(np.unique(y_train))}")
    print(f"Class distribution: {np.bincount(y_train)}")

    return XXX_train, XXX_valid, y_train, y_valid
