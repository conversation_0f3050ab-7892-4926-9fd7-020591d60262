"""
📊 EXPORT t-SNE TO SINGLE FOLDER
This script generates t-SNE visualizations at epochs 20, 40, 60, 80, 100
and exports ALL results into ONE organized folder.

SINGLE FOLDER OUTPUT - ALL MODELS TOGETHER
"""

# Core imports
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
import os
import datetime
import warnings
warnings.filterwarnings('ignore')

# TensorFlow imports
import tensorflow as tf
from tensorflow import keras
from sklearn.model_selection import train_test_split
from sklearn.utils.multiclass import type_of_target
from sklearn.manifold import TSNE
from sklearn.decomposition import PCA

print("📊 EXPORT t-SNE TO SINGLE FOLDER")
print("="*70)
print("Target epochs: 20, 40, 60, 80, 100")
print("Output: Single organized folder")
print("="*70)

# ============================================================================
# CREATE SINGLE OUTPUT FOLDER
# ============================================================================

# Create main output folder with timestamp
timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
main_output_dir = f"tsne_results_{timestamp}"
os.makedirs(main_output_dir, exist_ok=True)

# Create subfolders for organization
visualizations_dir = os.path.join(main_output_dir, "visualizations")
coordinates_dir = os.path.join(main_output_dir, "coordinates")
summaries_dir = os.path.join(main_output_dir, "summaries")
comparison_dir = os.path.join(main_output_dir, "model_comparison")

for dir_path in [visualizations_dir, coordinates_dir, summaries_dir, comparison_dir]:
    os.makedirs(dir_path, exist_ok=True)

print(f"📁 Created main output folder: {main_output_dir}")
print(f"   📊 Visualizations: {visualizations_dir}")
print(f"   📈 Coordinates: {coordinates_dir}")
print(f"   📋 Summaries: {summaries_dir}")
print(f"   🔄 Comparisons: {comparison_dir}")

# ============================================================================
# LOAD AND PREPARE DATA
# ============================================================================

print("\n📊 Loading augmented and reshaped data...")

# Load the processed data
data = np.load('augmented_reshaped_data_20250728_092500.npz')
reshaped_data = data['reshaped_data']      # Shape: (1232, 10, 4000)
reshaped_labels = data['reshaped_labels']  # Shape: (1232,)

print(f"   ✅ Loaded {len(reshaped_labels)} samples")
print(f"   📊 Input shape: {reshaped_data.shape[1:]}")
print(f"   🏷️  Classes: {len(np.unique(reshaped_labels))}")

print("🔄 Preparing data...")

# Convert labels to proper format
if np.max(reshaped_labels) > 10:
    unique_labels = np.unique(reshaped_labels)
    label_mapping = {old_label: new_label for new_label, old_label in enumerate(unique_labels)}
    mapped_labels = np.array([label_mapping[label] for label in reshaped_labels])
    print(f"   📝 Mapped {len(unique_labels)} unique labels to classes 0-{len(unique_labels)-1}")
else:
    mapped_labels = reshaped_labels.astype(int)

# Split data
X_train, X_valid, y_train, y_valid = train_test_split(
    reshaped_data, mapped_labels, test_size=0.2, random_state=42
)

# Fix label types if needed
if type_of_target(y_train) == 'continuous':
    print("⚠️  Converting continuous labels to discrete classes...")
    
    if hasattr(y_train, 'dtype') and np.issubdtype(y_train.dtype, np.floating):
        y_train = np.round(y_train).astype(int)
        y_valid = np.round(y_valid).astype(int)
    
    unique_labels = np.unique(np.concatenate([y_train, y_valid]))
    label_mapping = {old_label: new_label for new_label, old_label in enumerate(unique_labels)}
    
    y_train = np.array([label_mapping[label] for label in y_train])
    y_valid = np.array([label_mapping[label] for label in y_valid])
    
    print(f"✅ Labels converted. New type: {type_of_target(y_train)}")

print(f"   Training: {X_train.shape}, Validation: {X_valid.shape}")
print(f"   Classes: {len(np.unique(y_train))}")

# ============================================================================
# FEATURE EXTRACTION FUNCTION
# ============================================================================

def extract_features_from_model(model, X_data, model_name):
    """Extract features from model for t-SNE"""
    print(f"   🔍 Extracting features from {model_name}...")
    
    try:
        # Find the best layer for feature extraction
        feature_layer = None
        
        # Look for the last dense layer before output
        for i in range(len(model.layers) - 1, -1, -1):
            layer = model.layers[i]
            if hasattr(layer, 'units') and layer.units > 1:
                # Skip the output layer (usually has units = num_classes)
                if i < len(model.layers) - 1:
                    feature_layer = layer
                    break
        
        if feature_layer is not None:
            # Create feature extraction model
            feature_model = keras.Model(
                inputs=model.input,
                outputs=feature_layer.output
            )
            features = feature_model.predict(X_data, verbose=0)
            print(f"      ✅ Features from {feature_layer.name}: {features.shape}")
            return features
        else:
            # Use model predictions as features
            predictions = model.predict(X_data, verbose=0)
            print(f"      ✅ Using predictions as features: {predictions.shape}")
            return predictions
            
    except Exception as e:
        print(f"      ⚠️  Error extracting features: {e}")
        # Fallback to flattened input
        return X_data.reshape(X_data.shape[0], -1)

# ============================================================================
# t-SNE VISUALIZATION FUNCTION
# ============================================================================

def generate_tsne_visualization(features, labels, model_name, epoch):
    """Generate t-SNE visualization"""
    print(f"   📊 Generating t-SNE for {model_name} at epoch {epoch}...")
    
    try:
        # Reduce dimensionality if needed
        if features.shape[1] > 50:
            print(f"      🔄 Reducing dimensions with PCA: {features.shape[1]} -> 50")
            pca = PCA(n_components=50, random_state=42)
            features = pca.fit_transform(features)
        
        # Apply t-SNE
        perplexity = min(30, len(features) - 1)
        tsne = TSNE(
            n_components=2,
            perplexity=perplexity,
            n_iter=1000,
            random_state=42,
            verbose=0
        )
        
        tsne_results = tsne.fit_transform(features)
        
        # Create visualization
        fig, axes = plt.subplots(1, 2, figsize=(16, 6))
        
        # Plot 1: Colored by class
        unique_classes = np.unique(labels)
        colors = plt.cm.tab10(np.linspace(0, 1, len(unique_classes)))
        
        ax1 = axes[0]
        for i, class_label in enumerate(unique_classes):
            mask = labels == class_label
            ax1.scatter(
                tsne_results[mask, 0], 
                tsne_results[mask, 1],
                c=[colors[i]], 
                label=f'Class {int(class_label)}',
                alpha=0.7,
                s=30
            )
        
        ax1.set_title(f'{model_name} - t-SNE at Epoch {epoch}\nFeature Representation')
        ax1.set_xlabel('t-SNE Component 1')
        ax1.set_ylabel('t-SNE Component 2')
        ax1.legend(bbox_to_anchor=(1.05, 1), loc='upper left')
        ax1.grid(True, alpha=0.3)
        
        # Plot 2: Density view
        ax2 = axes[1]
        scatter = ax2.scatter(
            tsne_results[:, 0], 
            tsne_results[:, 1],
            c=labels,
            cmap='viridis',
            alpha=0.7,
            s=30
        )
        
        ax2.set_title(f'{model_name} - t-SNE at Epoch {epoch}\nClass Distribution')
        ax2.set_xlabel('t-SNE Component 1')
        ax2.set_ylabel('t-SNE Component 2')
        ax2.grid(True, alpha=0.3)
        
        # Add colorbar
        cbar = plt.colorbar(scatter, ax=ax2)
        cbar.set_label('Class Label')
        
        # Add epoch info
        epoch_text = f"Epoch {epoch}\nModel: {model_name}\nSamples: {len(labels)}"
        ax1.text(0.02, 0.98, epoch_text, transform=ax1.transAxes,
                verticalalignment='top', bbox=dict(boxstyle='round', 
                facecolor='lightblue', alpha=0.8))
        
        plt.tight_layout()
        
        # Save visualization to single folder
        filename = f"{model_name}_epoch_{epoch:03d}.png"
        filepath = os.path.join(visualizations_dir, filename)
        
        plt.savefig(filepath, dpi=300, bbox_inches='tight')
        plt.close()
        
        print(f"      ✅ Saved: {filename}")
        
        # Save coordinates to single folder
        coords_filename = f"{model_name}_epoch_{epoch:03d}_coords.npz"
        coords_filepath = os.path.join(coordinates_dir, coords_filename)
        
        np.savez_compressed(
            coords_filepath,
            tsne_coordinates=tsne_results,
            labels=labels,
            epoch=epoch,
            model_name=model_name,
            features_shape=features.shape
        )
        
        print(f"      ✅ Coordinates saved: {coords_filename}")
        
        return tsne_results
        
    except Exception as e:
        print(f"      ❌ Error generating t-SNE: {e}")
        return None

# ============================================================================
# MODEL SUMMARY FUNCTION
# ============================================================================

def create_model_summary(tsne_results, model_name, labels):
    """Create a summary visualization for one model across all epochs"""
    if len(tsne_results) == 0:
        return
    
    print(f"   📊 Creating summary for {model_name}...")
    
    try:
        epochs = sorted(tsne_results.keys())
        n_epochs = len(epochs)
        
        # Create subplot grid
        cols = min(3, n_epochs)
        rows = (n_epochs + cols - 1) // cols
        
        fig, axes = plt.subplots(rows, cols, figsize=(5*cols, 4*rows))
        if n_epochs == 1:
            axes = [axes]
        elif rows == 1:
            axes = axes.reshape(1, -1)
        
        unique_classes = np.unique(labels)
        colors = plt.cm.tab10(np.linspace(0, 1, len(unique_classes)))
        
        for i, epoch in enumerate(epochs):
            row = i // cols
            col = i % cols
            ax = axes[row, col] if rows > 1 else axes[col]
            
            tsne_coords = tsne_results[epoch]
            
            for j, class_label in enumerate(unique_classes):
                mask = labels == class_label
                ax.scatter(
                    tsne_coords[mask, 0], 
                    tsne_coords[mask, 1],
                    c=[colors[j]], 
                    label=f'Class {int(class_label)}' if i == 0 else "",
                    alpha=0.7,
                    s=20
                )
            
            ax.set_title(f'Epoch {epoch}')
            ax.set_xlabel('t-SNE Component 1')
            ax.set_ylabel('t-SNE Component 2')
            ax.grid(True, alpha=0.3)
            
            if i == 0:
                ax.legend(bbox_to_anchor=(1.05, 1), loc='upper left')
        
        # Hide empty subplots
        for i in range(n_epochs, rows * cols):
            row = i // cols
            col = i % cols
            if rows > 1:
                axes[row, col].set_visible(False)
            else:
                axes[col].set_visible(False)
        
        plt.suptitle(f'{model_name} - t-SNE Evolution Across Epochs', fontsize=16)
        plt.tight_layout()
        
        # Save summary to single folder
        summary_filename = f"{model_name}_summary.png"
        summary_filepath = os.path.join(summaries_dir, summary_filename)
        
        plt.savefig(summary_filepath, dpi=300, bbox_inches='tight')
        plt.close()
        
        print(f"      ✅ Summary saved: {summary_filename}")
        
    except Exception as e:
        print(f"      ❌ Error creating summary: {e}")

# ============================================================================
# MAIN PROCESSING FUNCTION
# ============================================================================

def export_tsne_for_model(model_path, model_name, X_valid, y_valid, 
                         target_epochs=[20, 40, 60, 80, 100]):
    """Export t-SNE visualizations for a specific model"""
    print(f"\n🤖 Processing {model_name}...")
    
    try:
        # Load model
        model = keras.models.load_model(model_path)
        print(f"   ✅ Loaded model: {model.count_params():,} parameters")
        
        # Extract features
        features = extract_features_from_model(model, X_valid, model_name)
        
        # Generate t-SNE for each target epoch
        tsne_results = {}
        for epoch in target_epochs:
            print(f"   📊 Epoch {epoch}...")
            tsne_coords = generate_tsne_visualization(
                features, y_valid, model_name, epoch
            )
            if tsne_coords is not None:
                tsne_results[epoch] = tsne_coords
        
        # Create summary visualization
        create_model_summary(tsne_results, model_name, y_valid)
        
        print(f"   🎉 {model_name} t-SNE export completed!")
        return tsne_results
        
    except Exception as e:
        print(f"   ❌ Error processing {model_name}: {e}")
        return {}

# ============================================================================
# MODEL COMPARISON FUNCTION
# ============================================================================

def create_model_comparison(all_model_results, y_valid, target_epochs):
    """Create comparison visualizations across all models"""
    print(f"\n📊 Creating model comparison visualizations...")
    
    try:
        # For each epoch, create a comparison across models
        for epoch in target_epochs:
            print(f"   📊 Comparison for epoch {epoch}...")
            
            # Count models that have this epoch
            available_models = [name for name, results in all_model_results.items() 
                              if epoch in results]
            
            if len(available_models) == 0:
                continue
            
            # Create subplot grid
            n_models = len(available_models)
            cols = min(2, n_models)
            rows = (n_models + cols - 1) // cols
            
            fig, axes = plt.subplots(rows, cols, figsize=(8*cols, 6*rows))
            if n_models == 1:
                axes = [axes]
            elif rows == 1:
                axes = axes.reshape(1, -1)
            
            unique_classes = np.unique(y_valid)
            colors = plt.cm.tab10(np.linspace(0, 1, len(unique_classes)))
            
            for i, model_name in enumerate(available_models):
                row = i // cols
                col = i % cols
                ax = axes[row, col] if rows > 1 else axes[col]
                
                tsne_coords = all_model_results[model_name][epoch]
                
                for j, class_label in enumerate(unique_classes):
                    mask = y_valid == class_label
                    ax.scatter(
                        tsne_coords[mask, 0], 
                        tsne_coords[mask, 1],
                        c=[colors[j]], 
                        label=f'Class {int(class_label)}' if i == 0 else "",
                        alpha=0.7,
                        s=20
                    )
                
                ax.set_title(f'{model_name}')
                ax.set_xlabel('t-SNE Component 1')
                ax.set_ylabel('t-SNE Component 2')
                ax.grid(True, alpha=0.3)
                
                if i == 0:
                    ax.legend(bbox_to_anchor=(1.05, 1), loc='upper left')
            
            # Hide empty subplots
            for i in range(n_models, rows * cols):
                row = i // cols
                col = i % cols
                if rows > 1:
                    axes[row, col].set_visible(False)
                else:
                    axes[col].set_visible(False)
            
            plt.suptitle(f'Model Comparison - Epoch {epoch}', fontsize=16)
            plt.tight_layout()
            
            # Save comparison
            comparison_filename = f"epoch_{epoch:03d}_comparison.png"
            comparison_filepath = os.path.join(comparison_dir, comparison_filename)
            
            plt.savefig(comparison_filepath, dpi=300, bbox_inches='tight')
            plt.close()
            
            print(f"      ✅ Comparison saved: {comparison_filename}")
        
        # Create overall comparison summary
        create_overall_summary(all_model_results, y_valid, target_epochs)
        
    except Exception as e:
        print(f"   ❌ Error creating model comparison: {e}")

def create_overall_summary(all_model_results, y_valid, target_epochs):
    """Create an overall summary showing all models and epochs"""
    print(f"   📊 Creating overall summary...")
    
    try:
        # Create a large grid: models x epochs
        model_names = list(all_model_results.keys())
        n_models = len(model_names)
        n_epochs = len(target_epochs)
        
        fig, axes = plt.subplots(n_models, n_epochs, figsize=(4*n_epochs, 3*n_models))
        if n_models == 1:
            axes = axes.reshape(1, -1)
        elif n_epochs == 1:
            axes = axes.reshape(-1, 1)
        
        unique_classes = np.unique(y_valid)
        colors = plt.cm.tab10(np.linspace(0, 1, len(unique_classes)))
        
        for i, model_name in enumerate(model_names):
            for j, epoch in enumerate(target_epochs):
                ax = axes[i, j]
                
                if epoch in all_model_results[model_name]:
                    tsne_coords = all_model_results[model_name][epoch]
                    
                    for k, class_label in enumerate(unique_classes):
                        mask = y_valid == class_label
                        ax.scatter(
                            tsne_coords[mask, 0], 
                            tsne_coords[mask, 1],
                            c=[colors[k]], 
                            alpha=0.7,
                            s=10
                        )
                    
                    ax.set_title(f'{model_name}\nEpoch {epoch}', fontsize=10)
                else:
                    ax.text(0.5, 0.5, 'No Data', ha='center', va='center', 
                           transform=ax.transAxes)
                    ax.set_title(f'{model_name}\nEpoch {epoch}', fontsize=10)
                
                ax.set_xticks([])
                ax.set_yticks([])
                ax.grid(True, alpha=0.3)
        
        plt.suptitle('Complete t-SNE Overview - All Models and Epochs', fontsize=16)
        plt.tight_layout()
        
        # Save overall summary
        overall_filename = "complete_overview.png"
        overall_filepath = os.path.join(main_output_dir, overall_filename)
        
        plt.savefig(overall_filepath, dpi=300, bbox_inches='tight')
        plt.close()
        
        print(f"      ✅ Overall summary saved: {overall_filename}")
        
    except Exception as e:
        print(f"      ❌ Error creating overall summary: {e}")

# ============================================================================
# MAIN EXECUTION
# ============================================================================

# Define models to process
models_to_process = [
    ('resnet_model.keras', 'ResNet'),
    ('1d_cnn_model.keras', '1D_CNN'),
    ('lstm_model.keras', 'LSTM'),
    ('cnn_lstm_model.keras', 'CNN_LSTM')
]

# Target epochs for t-SNE visualization
target_epochs = [20, 40, 60, 80, 100]

# Process each model and collect results
all_model_results = {}
successful_exports = 0

for model_path, model_name in models_to_process:
    if os.path.exists(model_path):
        results = export_tsne_for_model(
            model_path, model_name, X_valid, y_valid, target_epochs
        )
        if results:
            all_model_results[model_name] = results
            successful_exports += 1
    else:
        print(f"\n⚠️  Model not found: {model_path}")

# Create model comparisons
if len(all_model_results) > 1:
    create_model_comparison(all_model_results, y_valid, target_epochs)

# ============================================================================
# CREATE INDEX FILE
# ============================================================================

def create_index_file():
    """Create an index file listing all generated files"""
    print(f"\n📋 Creating index file...")
    
    index_content = f"""# t-SNE Visualization Results
Generated on: {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

## Summary
- **Models processed**: {successful_exports}/{len(models_to_process)}
- **Epochs visualized**: {target_epochs}
- **Validation samples**: {len(y_valid)}
- **Classes**: {len(np.unique(y_valid))}

## Folder Structure

### 📊 Individual Visualizations (`visualizations/`)
"""
    
    # List visualization files
    viz_files = sorted([f for f in os.listdir(visualizations_dir) if f.endswith('.png')])
    for file in viz_files:
        index_content += f"- `{file}`\n"
    
    index_content += f"""
### 📈 Coordinate Data (`coordinates/`)
"""
    
    # List coordinate files
    coord_files = sorted([f for f in os.listdir(coordinates_dir) if f.endswith('.npz')])
    for file in coord_files:
        index_content += f"- `{file}`\n"
    
    index_content += f"""
### 📋 Model Summaries (`summaries/`)
"""
    
    # List summary files
    summary_files = sorted([f for f in os.listdir(summaries_dir) if f.endswith('.png')])
    for file in summary_files:
        index_content += f"- `{file}`\n"
    
    if os.path.exists(comparison_dir) and os.listdir(comparison_dir):
        index_content += f"""
### 🔄 Model Comparisons (`model_comparison/`)
"""
        
        # List comparison files
        comp_files = sorted([f for f in os.listdir(comparison_dir) if f.endswith('.png')])
        for file in comp_files:
            index_content += f"- `{file}`\n"
    
    index_content += f"""
### 🎯 Overall Summary
- `complete_overview.png` - All models and epochs in one view

## Usage
1. **Individual visualizations**: View specific model-epoch combinations
2. **Coordinate data**: Load NPZ files for further analysis
3. **Model summaries**: See epoch progression for each model
4. **Model comparisons**: Compare different models at same epochs
5. **Complete overview**: See everything at once

## Analysis Notes
- t-SNE parameters: perplexity=30, n_iter=1000
- PCA preprocessing applied when features > 50 dimensions
- Consistent color scheme across all visualizations
- High-resolution exports (300 DPI)
"""
    
    # Save index file
    index_filepath = os.path.join(main_output_dir, "README.md")
    with open(index_filepath, 'w') as f:
        f.write(index_content)
    
    print(f"   ✅ Index file created: README.md")

create_index_file()

# ============================================================================
# FINAL SUMMARY
# ============================================================================

print(f"\n" + "="*70)
print(f"🎉 t-SNE EXPORT TO SINGLE FOLDER COMPLETED!")
print(f"="*70)
print(f"📁 Main output folder: {main_output_dir}")
print(f"📊 Successfully processed: {successful_exports}/{len(models_to_process)} models")
print(f"🎯 Epochs visualized: {target_epochs}")
print(f"📈 Validation samples: {len(y_valid)}")

# Count generated files
viz_count = len([f for f in os.listdir(visualizations_dir) if f.endswith('.png')])
coord_count = len([f for f in os.listdir(coordinates_dir) if f.endswith('.npz')])
summary_count = len([f for f in os.listdir(summaries_dir) if f.endswith('.png')])
comp_count = len([f for f in os.listdir(comparison_dir) if f.endswith('.png')]) if os.path.exists(comparison_dir) else 0

print(f"\n📋 Generated files:")
print(f"   📊 Individual visualizations: {viz_count}")
print(f"   📈 Coordinate files: {coord_count}")
print(f"   📋 Model summaries: {summary_count}")
print(f"   🔄 Model comparisons: {comp_count}")
print(f"   🎯 Overall summary: 1")
print(f"   📄 Index file: 1")

total_files = viz_count + coord_count + summary_count + comp_count + 2
print(f"\n🎉 Total files generated: {total_files}")

print(f"\n📁 Folder structure:")
print(f"   {main_output_dir}/")
print(f"   ├── visualizations/     ({viz_count} files)")
print(f"   ├── coordinates/        ({coord_count} files)")
print(f"   ├── summaries/          ({summary_count} files)")
print(f"   ├── model_comparison/   ({comp_count} files)")
print(f"   ├── complete_overview.png")
print(f"   └── README.md")

print(f"\n✅ All t-SNE results exported to single organized folder!")
print(f"📋 Check README.md for detailed file listing and usage instructions")
