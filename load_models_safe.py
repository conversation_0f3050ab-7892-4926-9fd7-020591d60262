"""
Safe model loader that attempts to load Keras models with fallback strategies
"""
import os
import sys

def load_models_with_tensorflow():
    """Attempt to load models using TensorFlow"""
    try:
        # Try to import TensorFlow
        import tensorflow as tf
        print("✅ TensorFlow imported successfully")
        
        model_files = [
            '1d_cnn_model.keras',
            'cnn_lstm_model.keras', 
            'lstm_model.keras',
            'resnet_model.keras'
        ]
        
        models = {}
        
        for model_file in model_files:
            if os.path.exists(model_file):
                model_name = model_file.replace('.keras', '')
                print(f"🔄 Loading {model_name}...")
                
                try:
                    # Try standard loading
                    models[model_name] = tf.keras.models.load_model(model_file)
                    print(f"✅ Successfully loaded: {model_name}")
                    
                    # Get model info
                    model = models[model_name]
                    print(f"   Input shape: {model.input_shape}")
                    print(f"   Output shape: {model.output_shape}")
                    print(f"   Parameters: {model.count_params():,}")
                    
                except Exception as e:
                    print(f"   ⚠️  Standard loading failed: {str(e)}")
                    
                    try:
                        # Try loading without compilation
                        models[model_name] = tf.keras.models.load_model(model_file, compile=False)
                        print(f"✅ Successfully loaded: {model_name} (without compilation)")
                        
                        # Get model info
                        model = models[model_name]
                        print(f"   Input shape: {model.input_shape}")
                        print(f"   Output shape: {model.output_shape}")
                        print(f"   Parameters: {model.count_params():,}")
                        
                    except Exception as e2:
                        print(f"   ❌ Failed to load {model_name}: {str(e2)}")
            else:
                print(f"❌ Model file not found: {model_file}")
        
        return models
        
    except ImportError as e:
        print(f"❌ TensorFlow import failed: {str(e)}")
        return None
    except Exception as e:
        print(f"❌ Unexpected error: {str(e)}")
        return None

def evaluate_models(models, X_valid, y_valid):
    """Evaluate loaded models on validation data"""
    if not models:
        print("⚠️  No models to evaluate")
        return None
    
    print("\n🔍 EVALUATING MODELS")
    print("="*50)
    
    results = {}
    
    for model_name, model in models.items():
        print(f"\nEvaluating {model_name}...")
        
        try:
            # Make predictions
            predictions = model.predict(X_valid, verbose=0)
            
            # Determine if this is classification or regression
            if len(predictions.shape) > 1 and predictions.shape[1] > 1:
                # Multi-class classification
                predicted_classes = predictions.argmax(axis=1)
                accuracy = (predicted_classes == y_valid).mean()
                print(f"   Accuracy: {accuracy:.4f}")
                
                results[model_name] = {
                    'type': 'classification',
                    'accuracy': accuracy,
                    'predictions': predictions,
                    'predicted_classes': predicted_classes
                }
            else:
                # Regression or binary classification
                if predictions.shape[1] == 1:
                    predictions = predictions.flatten()
                
                mse = ((predictions - y_valid) ** 2).mean()
                mae = abs(predictions - y_valid).mean()
                print(f"   MSE: {mse:.4f}")
                print(f"   MAE: {mae:.4f}")
                
                results[model_name] = {
                    'type': 'regression',
                    'mse': mse,
                    'mae': mae,
                    'predictions': predictions
                }
                
        except Exception as e:
            print(f"   ❌ Error evaluating {model_name}: {str(e)}")
            results[model_name] = {'error': str(e)}
    
    return results

def main():
    """Main function"""
    print("🚀 Safe Model Loader")
    print("="*50)
    
    # Try to load models
    models = load_models_with_tensorflow()
    
    if models:
        print(f"\n✅ Successfully loaded {len(models)} models")
        
        # Try to load data for evaluation
        try:
            print("\n🔄 Loading validation data...")
            
            # Import the data loader
            from data_only_loader import load_and_prepare_data
            data_dict = load_and_prepare_data()
            
            if data_dict:
                X_valid = data_dict['X_valid']
                y_valid = data_dict['y_valid']
                
                print(f"✅ Validation data loaded: {X_valid.shape}")
                
                # Evaluate models
                evaluation_results = evaluate_models(models, X_valid, y_valid)
                
                return {
                    'models': models,
                    'data': data_dict,
                    'evaluation': evaluation_results
                }
            else:
                print("❌ Failed to load data")
                return {'models': models, 'data': None, 'evaluation': None}
                
        except Exception as e:
            print(f"❌ Error loading data: {str(e)}")
            return {'models': models, 'data': None, 'evaluation': None}
    else:
        print("❌ No models could be loaded")
        return {'models': None, 'data': None, 'evaluation': None}

if __name__ == "__main__":
    # Load everything
    result = main()
    
    # Make variables available
    models = result['models']
    data = result['data']
    evaluation = result['evaluation']
    
    print("\n" + "="*50)
    print("📋 FINAL SUMMARY")
    print("="*50)
    
    if models:
        print(f"✅ Models loaded: {list(models.keys())}")
    else:
        print("❌ No models loaded")
    
    if data:
        print(f"✅ Data loaded: {data['X_train'].shape[0]} training samples")
    else:
        print("❌ No data loaded")
    
    if evaluation:
        print("✅ Model evaluation completed")
        for model_name, results in evaluation.items():
            if 'error' not in results:
                if results['type'] == 'classification':
                    print(f"   {model_name}: {results['accuracy']:.4f} accuracy")
                else:
                    print(f"   {model_name}: {results['mae']:.4f} MAE")
    else:
        print("❌ No evaluation performed")
    
    print("\nAvailable variables:")
    print("  - models: Dictionary of loaded Keras models")
    print("  - data: Dictionary with processed data")
    print("  - evaluation: Model evaluation results")
    
    if models:
        print("\nExample usage:")
        model_name = list(models.keys())[0]
        print(f"  models['{model_name}'].summary()")
        print(f"  predictions = models['{model_name}'].predict(data['X_valid'])")
