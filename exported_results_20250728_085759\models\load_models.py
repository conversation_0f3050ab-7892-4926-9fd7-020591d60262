# Model Loading Script
import tensorflow as tf
import numpy as np

print("Loading models...")
models = {}

try:
    models['1d_cnn_model'] = tf.keras.models.load_model('1d_cnn_model.keras')
    print(f"SUCCESS 1d_cnn_model: {models['1d_cnn_model'].count_params():,} parameters")
except Exception as e:
    print(f"FAILED to load 1d_cnn_model: {e}")

try:
    models['cnn_lstm_model'] = tf.keras.models.load_model('cnn_lstm_model.keras')
    print(f"SUCCESS cnn_lstm_model: {models['cnn_lstm_model'].count_params():,} parameters")
except Exception as e:
    print(f"FAILED to load cnn_lstm_model: {e}")

try:
    models['lstm_model'] = tf.keras.models.load_model('lstm_model.keras')
    print(f"SUCCESS lstm_model: {models['lstm_model'].count_params():,} parameters")
except Exception as e:
    print(f"FAILED to load lstm_model: {e}")

try:
    models['resnet_model'] = tf.keras.models.load_model('resnet_model.keras')
    print(f"SUCCESS resnet_model: {models['resnet_model'].count_params():,} parameters")
except Exception as e:
    print(f"FAILED to load resnet_model: {e}")


print(f"Successfully loaded {len(models)} models")
