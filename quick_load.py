"""
Quick data loader - loads data without TensorFlow dependencies
"""
import os
import numpy as np
from load_data import (
    load_mat_data, 
    prepare_input_data, 
    augment_time_series_data,
    reshape_time_series_data_v8,
    prepare_train_test_data
)

print("🚀 QUICK DATA LOADER")
print("="*40)

# Load data
print("Loading data...")
all_data = load_mat_data('data')
input_data, output_labels = prepare_input_data(all_data)
augmented_data, augmented_labels = augment_time_series_data(input_data, output_labels, num_augmentations=4)
reshaped_data, reshaped_labels = reshape_time_series_data_v8(augmented_data, augmented_labels, segments_per_new_sample=10, segment_length=4000)
X_train, X_valid, y_train, y_valid = prepare_train_test_data(reshaped_data, reshaped_labels, test_size=0.2, random_state=42)

# Create data dictionary
data = {
    'raw_data': all_data,
    'X_train': X_train,
    'X_valid': X_valid,
    'y_train': y_train,
    'y_valid': y_valid
}

print("✅ DATA LOADED SUCCESSFULLY!")
print(f"Training data: {X_train.shape}")
print(f"Validation data: {X_valid.shape}")
print(f"Training labels: {y_train.shape}")
print(f"Validation labels: {y_valid.shape}")
print(f"Number of classes: {len(np.unique(y_train))}")

# Check model files
print("\n🔍 AVAILABLE MODEL FILES:")
model_files = ['1d_cnn_model.keras', 'cnn_lstm_model.keras', 'lstm_model.keras', 'resnet_model.keras']
for f in model_files:
    if os.path.exists(f):
        size_mb = os.path.getsize(f) / (1024*1024)
        print(f"✅ {f} ({size_mb:.1f} MB)")
    else:
        print(f"❌ {f} not found")

print("\n📝 TO LOAD MODELS, USE:")
print("import tensorflow as tf")
print("models = {}")
for f in model_files:
    if os.path.exists(f):
        name = f.replace('.keras', '')
        print(f"models['{name}'] = tf.keras.models.load_model('{f}')")

print(f"\n🎯 READY TO USE:")
print("- data['X_train'] - Training data")
print("- data['X_valid'] - Validation data") 
print("- data['y_train'] - Training labels")
print("- data['y_valid'] - Validation labels")
print("- Load models manually with TensorFlow as shown above")
