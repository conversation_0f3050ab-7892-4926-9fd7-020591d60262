"""
🔮 MAKE PREDICTIONS WITH ALL 4 MODELS
This script loads all models and data, then makes predictions for classification
"""
import os
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from datetime import datetime

def load_data_for_prediction():
    """Load the exported data"""
    print("📊 Loading data for prediction...")
    
    # Check if we're in the export directory
    if os.path.exists('data/complete_dataset.npz'):
        data_path = 'data/complete_dataset.npz'
    elif os.path.exists('exported_results_20250728_085759/data/complete_dataset.npz'):
        data_path = 'exported_results_20250728_085759/data/complete_dataset.npz'
    else:
        # Try to load from current directory using the simple loader
        print("   Loading from current directory...")
        from simple_export import load_data_simple
        return load_data_simple()
    
    # Load from exported NPZ file
    data = np.load(data_path)
    
    result = {
        'X_train': data['X_train'],
        'X_valid': data['X_valid'],
        'y_train': data['y_train'],
        'y_valid': data['y_valid']
    }
    
    print(f"   ✅ Data loaded successfully!")
    print(f"   Training data: {result['X_train'].shape}")
    print(f"   Validation data: {result['X_valid'].shape}")
    
    return result

def load_models_for_prediction():
    """Load all 4 Keras models"""
    print("🤖 Loading models for prediction...")
    
    try:
        import tensorflow as tf
        print("   ✅ TensorFlow imported successfully")
    except ImportError:
        print("   ❌ TensorFlow not available. Please install: pip install tensorflow")
        return None
    
    model_files = {
        '1d_cnn_model': '1d_cnn_model.keras',
        'cnn_lstm_model': 'cnn_lstm_model.keras',
        'lstm_model': 'lstm_model.keras',
        'resnet_model': 'resnet_model.keras'
    }
    
    models = {}
    
    for name, file_path in model_files.items():
        print(f"   Loading {name}...")
        try:
            # Try loading with compile=False first (safer)
            model = tf.keras.models.load_model(file_path, compile=False)
            models[name] = model
            print(f"   ✅ {name}: {model.count_params():,} parameters")
            print(f"      Input shape: {model.input_shape}")
            print(f"      Output shape: {model.output_shape}")
        except Exception as e:
            print(f"   ❌ Failed to load {name}: {str(e)}")
    
    print(f"   📊 Successfully loaded {len(models)}/4 models")
    return models

def make_predictions(models, data):
    """Make predictions with all models"""
    print("\n🔮 Making predictions with all models...")
    
    X_valid = data['X_valid']
    y_valid = data['y_valid']
    
    predictions_results = {}
    
    for model_name, model in models.items():
        print(f"\n   Predicting with {model_name}...")
        
        try:
            # Make predictions
            raw_predictions = model.predict(X_valid, verbose=0)
            
            # Convert to class predictions
            if len(raw_predictions.shape) > 1 and raw_predictions.shape[1] > 1:
                # Multi-class classification
                predicted_classes = np.argmax(raw_predictions, axis=1)
                prediction_probabilities = raw_predictions
            else:
                # Binary or regression - convert to classes
                if raw_predictions.shape[1] == 1:
                    raw_predictions = raw_predictions.flatten()
                predicted_classes = np.round(raw_predictions).astype(int)
                prediction_probabilities = raw_predictions
            
            # Calculate accuracy
            accuracy = np.mean(predicted_classes == y_valid)
            
            # Store results
            predictions_results[model_name] = {
                'predicted_classes': predicted_classes,
                'raw_predictions': raw_predictions,
                'probabilities': prediction_probabilities,
                'accuracy': accuracy,
                'model_output_shape': model.output_shape
            }
            
            print(f"   ✅ {model_name}: Accuracy = {accuracy:.4f} ({accuracy*100:.2f}%)")
            print(f"      Predictions shape: {raw_predictions.shape}")
            print(f"      Sample predictions: {predicted_classes[:5]}")
            
        except Exception as e:
            print(f"   ❌ Error with {model_name}: {str(e)}")
            predictions_results[model_name] = {'error': str(e)}
    
    return predictions_results

def analyze_predictions(predictions_results, data):
    """Analyze and compare predictions from all models"""
    print("\n📊 ANALYZING PREDICTIONS")
    print("="*60)
    
    y_valid = data['y_valid']
    
    # Create comparison DataFrame
    comparison_data = {
        'actual': y_valid
    }
    
    # Add predictions from each model
    for model_name, results in predictions_results.items():
        if 'predicted_classes' in results:
            comparison_data[f'{model_name}_pred'] = results['predicted_classes']
            comparison_data[f'{model_name}_acc'] = results['accuracy']
    
    # Create DataFrame
    df = pd.DataFrame(comparison_data)
    
    # Print summary statistics
    print("🎯 ACCURACY SUMMARY:")
    for model_name, results in predictions_results.items():
        if 'accuracy' in results:
            print(f"   {model_name}: {results['accuracy']:.4f} ({results['accuracy']*100:.2f}%)")
    
    # Find best performing model
    best_model = max(
        [(name, results['accuracy']) for name, results in predictions_results.items() 
         if 'accuracy' in results],
        key=lambda x: x[1]
    )
    print(f"\n🏆 BEST MODEL: {best_model[0]} with {best_model[1]:.4f} accuracy")
    
    # Class-wise analysis
    print(f"\n📈 CLASS DISTRIBUTION:")
    print(f"   Actual classes: {np.bincount(y_valid)}")
    
    for model_name, results in predictions_results.items():
        if 'predicted_classes' in results:
            pred_classes = results['predicted_classes']
            print(f"   {model_name}: {np.bincount(pred_classes, minlength=len(np.bincount(y_valid)))}")
    
    return df

def create_prediction_report(predictions_results, data, comparison_df):
    """Create detailed prediction report"""
    print("\n📄 Creating prediction report...")
    
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    report_file = f"prediction_report_{timestamp}.txt"
    
    with open(report_file, 'w') as f:
        f.write("PREDICTION REPORT\n")
        f.write("="*50 + "\n")
        f.write(f"Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
        
        f.write("DATASET INFO:\n")
        f.write(f"Validation samples: {len(data['y_valid'])}\n")
        f.write(f"Number of classes: {len(np.unique(data['y_valid']))}\n")
        f.write(f"Input shape: {data['X_valid'].shape}\n\n")
        
        f.write("MODEL PERFORMANCE:\n")
        for model_name, results in predictions_results.items():
            if 'accuracy' in results:
                f.write(f"{model_name}:\n")
                f.write(f"  Accuracy: {results['accuracy']:.4f} ({results['accuracy']*100:.2f}%)\n")
                f.write(f"  Output shape: {results['model_output_shape']}\n")
                
                # Confusion matrix info
                y_true = data['y_valid']
                y_pred = results['predicted_classes']
                
                # Calculate per-class accuracy
                for class_id in np.unique(y_true):
                    mask = y_true == class_id
                    if np.sum(mask) > 0:
                        class_acc = np.mean(y_pred[mask] == class_id)
                        f.write(f"  Class {class_id} accuracy: {class_acc:.4f}\n")
                f.write("\n")
        
        # Best model
        best_model = max(
            [(name, results['accuracy']) for name, results in predictions_results.items() 
             if 'accuracy' in results],
            key=lambda x: x[1]
        )
        f.write(f"BEST MODEL: {best_model[0]} ({best_model[1]:.4f} accuracy)\n\n")
        
        # Sample predictions
        f.write("SAMPLE PREDICTIONS (first 20):\n")
        f.write("Actual: " + " ".join([f"{int(x):2d}" for x in data['y_valid'][:20]]) + "\n")
        for model_name, results in predictions_results.items():
            if 'predicted_classes' in results:
                pred_str = " ".join([f"{int(x):2d}" for x in results['predicted_classes'][:20]])
                f.write(f"{model_name:15s}: {pred_str}\n")
    
    print(f"   ✅ Report saved to: {report_file}")
    return report_file

def save_predictions_csv(predictions_results, data):
    """Save predictions to CSV file"""
    print("\n💾 Saving predictions to CSV...")
    
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    csv_file = f"predictions_{timestamp}.csv"
    
    # Create DataFrame with all predictions
    csv_data = {
        'sample_id': range(len(data['y_valid'])),
        'actual_label': data['y_valid']
    }
    
    # Add predictions from each model
    for model_name, results in predictions_results.items():
        if 'predicted_classes' in results:
            csv_data[f'{model_name}_prediction'] = results['predicted_classes']
            csv_data[f'{model_name}_correct'] = (results['predicted_classes'] == data['y_valid']).astype(int)
    
    df = pd.DataFrame(csv_data)
    df.to_csv(csv_file, index=False)
    
    print(f"   ✅ Predictions saved to: {csv_file}")
    return csv_file

def main():
    """Main prediction function"""
    print("🔮 MAKING PREDICTIONS WITH ALL 4 MODELS")
    print("="*60)
    
    # Load data
    data = load_data_for_prediction()
    if not data:
        print("❌ Failed to load data")
        return
    
    # Load models
    models = load_models_for_prediction()
    if not models:
        print("❌ Failed to load models")
        return
    
    # Make predictions
    predictions_results = make_predictions(models, data)
    
    # Analyze results
    comparison_df = analyze_predictions(predictions_results, data)
    
    # Create reports
    report_file = create_prediction_report(predictions_results, data, comparison_df)
    csv_file = save_predictions_csv(predictions_results, data)
    
    print("\n" + "="*60)
    print("✅ PREDICTION COMPLETE!")
    print("="*60)
    print(f"📊 Processed {len(data['y_valid'])} validation samples")
    print(f"🤖 Used {len(models)} models")
    print(f"📄 Report: {report_file}")
    print(f"💾 CSV: {csv_file}")
    
    # Show quick summary
    print(f"\n🏆 QUICK RESULTS:")
    for model_name, results in predictions_results.items():
        if 'accuracy' in results:
            print(f"   {model_name}: {results['accuracy']:.4f} accuracy")
    
    return {
        'predictions': predictions_results,
        'data': data,
        'models': models,
        'comparison_df': comparison_df
    }

if __name__ == "__main__":
    result = main()
    
    # Make variables available
    if result:
        predictions = result['predictions']
        data = result['data']
        models = result['models']
        comparison_df = result['comparison_df']
        
        print(f"\n📋 Available variables:")
        print(f"   - predictions: Dictionary with all model predictions")
        print(f"   - data: Loaded dataset")
        print(f"   - models: Loaded Keras models")
        print(f"   - comparison_df: DataFrame comparing all predictions")
        
        print(f"\n💡 Example usage:")
        print(f"   predictions['lstm_model']['accuracy']")
        print(f"   predictions['resnet_model']['predicted_classes'][:10]")
        print(f"   comparison_df.head()")
