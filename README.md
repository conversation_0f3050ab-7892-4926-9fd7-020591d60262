# 🔮 Model Prediction and Results Extraction

This repository contains 4 trained Keras models for time series classification and all necessary files to load models, process data, and extract prediction results.

## 📁 Required Files

### 🤖 **Model Files (Required)**
```
1d_cnn_model.keras      # 1D CNN model (23.5 MB)
cnn_lstm_model.keras    # CNN-LSTM hybrid model (24.3 MB)
lstm_model.keras        # LSTM model (12.3 MB)
resnet_model.keras      # ResNet model (36.2 MB)
```

### 📊 **Data Files (Required)**
```
augmented_data_20250728_091843.npz    # Augmented dataset (247 validation samples)
```
**OR** use the original data processing:
```
data/                   # Original .mat files directory
├── NamO0.mat          # Class 0 data
├── NamO1.mat          # Class 1 data
├── ...
└── NamO10.mat         # Class 10 data
```

### 🔧 **Processing Scripts (Required)**
```
READY_TO_RUN_PREDICTIONS_20250728_091751.py    # Main prediction script
fixed_data_loader.py                           # Data loading with augmentation
load_data.py                                   # Core data processing functions
```

### 📈 **Results Files (Optional)**
```
model_comparison_results_20250628_001945.pkl   # Previous training results
```

---

## 🚀 Quick Start

### **Method 1: Use Pre-processed Data (Recommended)**

1. **Required files:**
   ```
   READY_TO_RUN_PREDICTIONS_20250728_091751.py
   augmented_data_20250728_091843.npz
   1d_cnn_model.keras
   cnn_lstm_model.keras
   lstm_model.keras
   resnet_model.keras
   ```

2. **Run predictions:**
   ```bash
   python READY_TO_RUN_PREDICTIONS_20250728_091751.py
   ```

### **Method 2: Process Data from Scratch**

1. **Required files:**
   ```
   fixed_data_loader.py
   load_data.py
   data/NamO0.mat through data/NamO10.mat
   1d_cnn_model.keras
   cnn_lstm_model.keras
   lstm_model.keras
   resnet_model.keras
   ```

2. **Run with data processing:**
   ```python
   from fixed_data_loader import load_complete_dataset_with_augmentation
   import tensorflow as tf
   
   # Load and process data
   data = load_complete_dataset_with_augmentation()
   
   # Load models and predict
   # (see detailed code below)
   ```

---

## 📋 Environment Setup

### **Requirements**
```bash
pip install tensorflow==2.13.0
pip install numpy==1.24.3
pip install pandas
pip install scikit-learn
pip install scipy
pip install matplotlib
```

### **Alternative Environment**
```bash
conda create -n model_prediction python=3.9
conda activate model_prediction
pip install tensorflow numpy pandas scikit-learn scipy matplotlib
```

---

## 🔮 Model Loading and Prediction

### **Complete Example**
```python
import tensorflow as tf
import numpy as np
import pandas as pd

# STEP 1: Load pre-processed data
data = np.load('augmented_data_20250728_091843.npz')
X_valid = data['X_valid']  # Shape: (247, 10, 4000)
y_valid = data['y_valid']  # Shape: (247,)

print(f"Loaded {len(y_valid)} validation samples")
print(f"Input shape: {X_valid.shape[1:]}")
print(f"Classes: {len(np.unique(y_valid))}")

# STEP 2: Load all 4 models
models = {}
model_files = {
    '1d_cnn_model': '1d_cnn_model.keras',
    'cnn_lstm_model': 'cnn_lstm_model.keras',
    'lstm_model': 'lstm_model.keras',
    'resnet_model': 'resnet_model.keras'
}

for name, file_path in model_files.items():
    try:
        models[name] = tf.keras.models.load_model(file_path)
        print(f"✅ {name}: {models[name].count_params():,} parameters")
    except Exception as e:
        print(f"❌ {name}: {e}")

# STEP 3: Make predictions
predictions_results = {}

for model_name, model in models.items():
    print(f"\nPredicting with {model_name}...")
    
    # Make predictions
    raw_predictions = model.predict(X_valid, verbose=0)
    
    # Convert to class predictions
    if len(raw_predictions.shape) > 1 and raw_predictions.shape[1] > 1:
        predicted_classes = np.argmax(raw_predictions, axis=1)
        max_probs = np.max(raw_predictions, axis=1)
    else:
        predicted_classes = np.round(raw_predictions.flatten()).astype(int)
        predicted_classes = np.clip(predicted_classes, 0, 10)
        max_probs = np.abs(raw_predictions.flatten())
    
    # Calculate accuracy
    accuracy = np.mean(predicted_classes == y_valid)
    avg_confidence = np.mean(max_probs)
    
    predictions_results[model_name] = {
        'predicted_classes': predicted_classes,
        'accuracy': accuracy,
        'avg_confidence': avg_confidence
    }
    
    print(f"Accuracy: {accuracy:.4f} ({accuracy*100:.2f}%)")
    print(f"Confidence: {avg_confidence:.4f}")

# STEP 4: Compare results
print("\n🏆 FINAL RESULTS:")
for model_name, results in predictions_results.items():
    acc = results['accuracy']
    conf = results['avg_confidence']
    print(f"{model_name:15s}: {acc:.4f} ({acc*100:.2f}%) | Confidence: {conf:.4f}")

# Find best model
best_model = max(predictions_results.items(), key=lambda x: x[1]['accuracy'])
print(f"\nBest model: {best_model[0]} ({best_model[1]['accuracy']:.4f} accuracy)")
```

---

## 📊 Data Specifications

### **Input Data Format**
- **Shape**: (247, 10, 4000)
- **Type**: float64
- **Range**: -0.008392 to 0.008913
- **Mean**: ~0 (zero-centered)
- **Channels**: 10 sensor channels
- **Time points**: 4000 per channel

### **Output Labels**
- **Classes**: 11 (labeled 0-10)
- **Type**: integer
- **Distribution**: Moderately balanced across classes

### **Model Input/Output**
```
Input:  (batch_size, 10, 4000)
Output: (batch_size, 11) for classification probabilities
```

---

## 📈 Results Extraction

### **Prediction Results**
The prediction script generates:

1. **CSV Files:**
   ```
   final_predictions_YYYYMMDD_HHMMSS.csv     # All predictions
   ```

2. **Summary Files:**
   ```
   prediction_summary_YYYYMMDD_HHMMSS.txt    # Performance summary
   ```

### **Results Format**
```python
# CSV columns:
sample_id, actual, 1d_cnn_model_pred, 1d_cnn_model_correct, 1d_cnn_model_confidence,
cnn_lstm_model_pred, cnn_lstm_model_correct, cnn_lstm_model_confidence,
lstm_model_pred, lstm_model_correct, lstm_model_confidence,
resnet_model_pred, resnet_model_correct, resnet_model_confidence
```

### **Performance Metrics**
- **Overall accuracy** per model
- **Per-class accuracy** breakdown
- **Confidence scores** for predictions
- **Model agreement** analysis

---

## 🔧 Troubleshooting

### **Common Issues**

1. **TensorFlow Import Error:**
   ```bash
   pip install --upgrade tensorflow
   # or
   conda install tensorflow-gpu
   ```

2. **Model Loading Error:**
   ```python
   # Try loading without compilation
   model = tf.keras.models.load_model('model.keras', compile=False)
   ```

3. **Data Shape Mismatch:**
   ```python
   # Verify data shape
   print(f"Expected: (batch, 10, 4000)")
   print(f"Actual: {X_valid.shape}")
   ```

4. **Memory Issues:**
   ```python
   # Process in batches
   batch_size = 32
   predictions = []
   for i in range(0, len(X_valid), batch_size):
       batch = X_valid[i:i+batch_size]
       pred = model.predict(batch)
       predictions.append(pred)
   ```

### **File Verification**
```python
import os

required_files = [
    'augmented_data_20250728_091843.npz',
    '1d_cnn_model.keras',
    'cnn_lstm_model.keras',
    'lstm_model.keras',
    'resnet_model.keras'
]

for file in required_files:
    if os.path.exists(file):
        size_mb = os.path.getsize(file) / (1024*1024)
        print(f"✅ {file}: {size_mb:.1f} MB")
    else:
        print(f"❌ {file}: NOT FOUND")
```

---

## 📋 File Checklist

### **Minimum Required Files**
- [ ] `augmented_data_20250728_091843.npz` (or data/ directory)
- [ ] `1d_cnn_model.keras`
- [ ] `cnn_lstm_model.keras`
- [ ] `lstm_model.keras`
- [ ] `resnet_model.keras`
- [ ] `READY_TO_RUN_PREDICTIONS_20250728_091751.py`

### **Optional Files**
- [ ] `model_comparison_results_20250628_001945.pkl`
- [ ] `fixed_data_loader.py` (for data processing)
- [ ] `load_data.py` (for data processing)

### **Expected Outputs**
- [ ] `final_predictions_YYYYMMDD_HHMMSS.csv`
- [ ] `prediction_summary_YYYYMMDD_HHMMSS.txt`

---

## 🎯 Expected Results

### **Performance Range**
- **1D CNN**: ~85% accuracy
- **CNN-LSTM**: ~88% accuracy  
- **LSTM**: ~86% accuracy
- **ResNet**: ~92% accuracy (typically best)

### **Output Example**
```
🎯 ACCURACY SUMMARY:
   1d_cnn_model    : 0.8502 (85.02%) | Confidence: 0.7234
   cnn_lstm_model  : 0.8866 (88.66%) | Confidence: 0.7891
   lstm_model      : 0.8623 (86.23%) | Confidence: 0.7456
   resnet_model    : 0.9231 (92.31%) | Confidence: 0.8123

🏆 BEST MODEL: resnet_model
```

---

## 📞 Support

If you encounter issues:
1. Verify all required files are present
2. Check TensorFlow installation
3. Ensure data shapes match expected format
4. Review error messages for specific issues

**Ready to predict? Run the prediction script and get your classification results!** 🚀
