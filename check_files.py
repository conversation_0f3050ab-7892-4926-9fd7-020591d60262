"""
🔍 SIMPLE FILE CHECKER
Check if all required files are present (without TensorFlow import)
"""
import os
import numpy as np

def check_files():
    """Check all required files"""
    print("🔍 CHECKING REQUIRED FILES")
    print("="*50)
    
    # Model files
    print("\n🤖 MODEL FILES:")
    model_files = [
        '1d_cnn_model.keras',
        'cnn_lstm_model.keras',
        'lstm_model.keras',
        'resnet_model.keras'
    ]
    
    models_found = 0
    total_model_size = 0
    
    for model_file in model_files:
        if os.path.exists(model_file):
            size_mb = os.path.getsize(model_file) / (1024*1024)
            total_model_size += size_mb
            print(f"   ✅ {model_file}: {size_mb:.1f} MB")
            models_found += 1
        else:
            print(f"   ❌ {model_file}: NOT FOUND")
    
    # Data files
    print(f"\n📊 DATA FILES:")
    data_file = 'augmented_data_20250728_091843.npz'
    prediction_script = 'READY_TO_RUN_PREDICTIONS_20250728_091751.py'
    
    data_found = 0
    
    if os.path.exists(data_file):
        size_mb = os.path.getsize(data_file) / (1024*1024)
        print(f"   ✅ {data_file}: {size_mb:.1f} MB")
        data_found += 1
        
        # Check data contents
        try:
            data = np.load(data_file)
            print(f"      📈 X_valid: {data['X_valid'].shape}")
            print(f"      📈 y_valid: {data['y_valid'].shape}")
            print(f"      📈 Classes: {len(np.unique(data['y_valid']))}")
        except Exception as e:
            print(f"      ⚠️  Error reading data: {e}")
    else:
        print(f"   ❌ {data_file}: NOT FOUND")
    
    if os.path.exists(prediction_script):
        size_kb = os.path.getsize(prediction_script) / 1024
        print(f"   ✅ {prediction_script}: {size_kb:.1f} KB")
        data_found += 1
    else:
        print(f"   ❌ {prediction_script}: NOT FOUND")
    
    # Alternative data source
    print(f"\n📁 ORIGINAL DATA (Alternative):")
    data_dir = 'data'
    if os.path.exists(data_dir):
        mat_files = [f for f in os.listdir(data_dir) if f.endswith('.mat')]
        print(f"   ✅ data/ directory: {len(mat_files)} .mat files")
    else:
        print(f"   ❌ data/ directory: NOT FOUND")
    
    # Summary
    print(f"\n" + "="*50)
    print("📋 SUMMARY")
    print("="*50)
    print(f"🤖 Models: {models_found}/4 ({total_model_size:.1f} MB total)")
    print(f"📊 Data files: {data_found}/2")
    
    if models_found == 4 and data_found >= 1:
        print(f"\n✅ ALL REQUIRED FILES PRESENT!")
        print(f"🚀 Ready to run predictions")
        print(f"\nNext steps:")
        print(f"1. Set up TensorFlow environment")
        print(f"2. Run: python {prediction_script}")
    else:
        print(f"\n⚠️  MISSING FILES:")
        if models_found < 4:
            missing_models = [f for f in model_files if not os.path.exists(f)]
            print(f"   Missing models: {missing_models}")
        if data_found < 1:
            print(f"   Missing data files")
    
    return models_found == 4 and data_found >= 1

if __name__ == "__main__":
    ready = check_files()
    
    if ready:
        print(f"\n🎉 Everything is ready for predictions!")
    else:
        print(f"\n💡 Download missing files and try again.")
