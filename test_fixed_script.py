"""
🔍 TEST FIXED SCRIPT
This script tests if the fixed prediction script has no syntax errors
"""

def test_script_syntax():
    """Test if the script has correct syntax"""
    print("🔍 Testing fixed script syntax...")
    
    try:
        # Try to compile the script
        with open('CLEAN_RUN_PY_PREDICTIONS.py', 'r', encoding='utf-8') as f:
            script_content = f.read()

        # Compile to check for syntax errors
        compile(script_content, 'CLEAN_RUN_PY_PREDICTIONS.py', 'exec')
        
        print("✅ Script syntax is correct!")
        return True
        
    except SyntaxError as e:
        print(f"❌ Syntax error found: {e}")
        print(f"   Line {e.lineno}: {e.text}")
        return False
    except Exception as e:
        print(f"❌ Other error: {e}")
        return False

def check_required_files():
    """Check if required files exist"""
    print("\n🔍 Checking required files...")
    
    import os
    
    required_files = [
        'augmented_reshaped_data_20250728_092500.npz',
        'resnet_model.keras',
        '1d_cnn_model.keras',
        'lstm_model.keras',
        'cnn_lstm_model.keras'
    ]
    
    all_present = True
    for file in required_files:
        if os.path.exists(file):
            size_mb = os.path.getsize(file) / (1024*1024)
            print(f"   ✅ {file}: {size_mb:.1f} MB")
        else:
            print(f"   ❌ {file}: NOT FOUND")
            all_present = False
    
    return all_present

def main():
    """Main test function"""
    print("🔍 TESTING FIXED PREDICTION SCRIPT")
    print("="*50)
    
    # Test syntax
    syntax_ok = test_script_syntax()
    
    # Check files
    files_ok = check_required_files()
    
    print("\n" + "="*50)
    print("📋 TEST RESULTS")
    print("="*50)
    print(f"Script syntax: {'✅ OK' if syntax_ok else '❌ ERROR'}")
    print(f"Required files: {'✅ OK' if files_ok else '❌ MISSING'}")
    
    if syntax_ok and files_ok:
        print(f"\n🎉 ALL TESTS PASSED!")
        print(f"✅ CLEAN_RUN_PY_PREDICTIONS.py is ready to run")
        print(f"\n🚀 To run predictions:")
        print(f"   python CLEAN_RUN_PY_PREDICTIONS.py")
    else:
        print(f"\n⚠️  ISSUES FOUND:")
        if not syntax_ok:
            print(f"   - Fix syntax errors in the script")
        if not files_ok:
            print(f"   - Ensure all required files are present")
    
    return syntax_ok and files_ok

if __name__ == "__main__":
    success = main()
    
    if success:
        print(f"\n💡 The fixed script is ready to run in a TensorFlow environment!")
    else:
        print(f"\n💡 Fix the issues above before running the script.")
