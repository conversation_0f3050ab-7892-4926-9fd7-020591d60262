# Model Loading Script
import tensorflow as tf
import numpy as np

# Load all models
models = {}
models['1d_cnn_model'] = tf.keras.models.load_model('1d_cnn_model.keras')
models['cnn_lstm_model'] = tf.keras.models.load_model('cnn_lstm_model.keras')
models['lstm_model'] = tf.keras.models.load_model('lstm_model.keras')
models['resnet_model'] = tf.keras.models.load_model('resnet_model.keras')

# Verify models loaded
for name, model in models.items():
    print(f'{name}: {model.count_params():,} parameters')
    print(f'  Input shape: {model.input_shape}')
    print(f'  Output shape: {model.output_shape}')
