# %% [markdown]
# # Complete Time Series Classification Model Comparison
# ## ResNet vs 1D CNN vs LSTM vs CNN-LSTM
# 
# This notebook contains all model implementations and comparison tools in one place for easy editing and experimentation.

# %% [markdown]
# ## 1. Import Libraries and Setup

# %%
# Core libraries
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from copy import deepcopy
import time
import pickle
import warnings
warnings.filterwarnings('ignore')

# TensorFlow and Keras
import tensorflow as tf
from tensorflow import keras
from tensorflow.keras.utils import to_categorical


# Scikit-learn
from sklearn.utils import check_random_state
from sklearn.preprocessing import LabelEncoder
from sklearn.metrics import (
    confusion_matrix, classification_report, accuracy_score,
    precision_recall_fscore_support, ConfusionMatrixDisplay, f1_score
)
from sklearn.manifold import TSNE


# Set random seeds for reproducibility
np.random.seed(42)
tf.random.set_seed(42)


# Set plotting style
sns.set_style('whitegrid')

# Configuration constants
OPTIMAL_BATCH_SIZE = 32  # Optimal batch size for CPU training

# CPU-optimized optimizer function
def get_cpu_optimized_optimizer():
    """Returns an optimizer optimized for CPU training."""
    return keras.optimizers.Adam(
        learning_rate=0.001,
        beta_1=0.9,
        beta_2=0.999,
        epsilon=1e-7
    )

print("\n✅ All libraries imported successfully!")
print(f"TensorFlow version: {tf.__version__}")
print(f"NumPy version: {np.__version__}")
print(f"Pandas version: {pd.__version__}")


plt.rcdefaults()

# %% [markdown]
# ## 2. t-SNE Visualization Callback

# %%
class TSNECallback(keras.callbacks.Callback):
    """Custom callback for t-SNE visualization during training."""
    
    def __init__(self, model_name, XXX_valid, y_valid, target_epochs=None):
        super().__init__()
        self.model_name = model_name
        self.XXX_valid = XXX_valid
        self.y_valid = y_valid
        self.target_epochs = target_epochs or []
        
    def on_epoch_end(self, epoch, logs=None):
        current_epoch = epoch + 1
        
        if current_epoch in self.target_epochs:
            try:
                # Get intermediate layer output for visualization
                if hasattr(self.model, 'layers') and len(self.model.layers) > 1:
                    # Get features from second-to-last layer
                    feature_layer = self.model.layers[-2]
                    feature_model = keras.Model(
                        inputs=self.model.input,
                        outputs=feature_layer.output
                    )
                    features = feature_model.predict(self.XXX_valid, verbose=0)
                    
                    # Flatten features if needed
                    if len(features.shape) > 2:
                        features = features.reshape(features.shape[0], -1)
                    
                    # Apply t-SNE
                    # Adaptive perplexity based on sample size
                    n_samples = features.shape[0]
                    perplexity = min(30, max(5, n_samples // 4))
                    
                    tsne = TSNE(
                        n_components=2,
                        random_state=42,
                        perplexity=perplexity,
                        max_iter=300
                    )
                    
                    tsne_features = tsne.fit_transform(features)
                    
                    # Calculate silhouette score
                    from sklearn.metrics import silhouette_score
                    silhouette_avg = silhouette_score(tsne_features, self.y_valid)
                    
                    print(f"Epoch {current_epoch}: Silhouette Score = {silhouette_avg:.4f}")
                    
                    # Plot t-SNE
                    plt.figure(figsize=(8, 6))
                    scatter = plt.scatter(
                        tsne_features[:, 0], 
                        tsne_features[:, 1], 
                        c=self.y_valid, 
                        cmap='viridis', 
                        alpha=0.7
                    )
                    plt.colorbar(scatter)
                    plt.title(f't-SNE Visualization - {self.model_name.upper()} (Epoch {current_epoch})')
                    plt.xlabel('t-SNE Component 1')
                    plt.ylabel('t-SNE Component 2')
                    plt.show()
                    
            except Exception as e:
                print(f"t-SNE visualization failed for epoch {current_epoch}: {str(e)}")

print("✅ TSNECallback class defined successfully!")

# %% [markdown]
# ## 3. Base Model Class

# %%
class BaseTimeSeriesClassifier:
    """Base class for time series classifiers."""
    
    def __init__(
        self,
        n_epochs=100,
        callbacks=None,
        verbose=False,
        loss="categorical_crossentropy",
        metrics=None,
        batch_size=128,
        random_state=None,
        activation="softmax",
        use_bias=True,
        optimizer=None,
    ):
        self.n_epochs = n_epochs
        self.callbacks = callbacks
        self.verbose = verbose
        self.loss = loss
        self.metrics = metrics
        self.batch_size = batch_size
        self.random_state = random_state
        self.activation = activation
        self.use_bias = use_bias
        
        # Set CPU-optimized optimizer if not provided
        if optimizer is None:
            self.optimizer = get_cpu_optimized_optimizer()
        else:
            self.optimizer = optimizer
        
        # Use optimal batch size if default is used
        if batch_size == 128:  # Default value
            self.batch_size = OPTIMAL_BATCH_SIZE

        self.history = None
        self.model_ = None
        self.label_encoder = LabelEncoder()
        self.n_classes_ = None
        self.input_shape = None

    def _convert_y_to_keras(self, y):
        """Convert labels to one-hot encoded format."""
        if not hasattr(self.label_encoder, 'classes_'):
            self.label_encoder.fit(y)
            self.n_classes_ = len(self.label_encoder.classes_)
        
        y_int = self.label_encoder.transform(y)
        y_onehot = keras.utils.to_categorical(y_int, num_classes=self.n_classes_)
        return y_onehot

    def fit(self, X, y, **kwargs):
        """Fit the model."""
        y_onehot = self._convert_y_to_keras(y)
        check_random_state(self.random_state)
        self.input_shape = X.shape[1:]
        
        self.model_ = self.build_model(self.input_shape, self.n_classes_)
        
        if self.verbose or kwargs.get("verbose", False):
            self.model_.summary()

        fit_params = dict(
            batch_size=kwargs.get("batch_size", self.batch_size),
            epochs=kwargs.get("epochs", self.n_epochs),
            verbose=kwargs.get("verbose", self.verbose),
            callbacks=deepcopy(self.callbacks) if self.callbacks else [],
        )
        
        if "validation_data" in kwargs:
            val_X, val_y = kwargs["validation_data"]
            val_y_onehot = self._convert_y_to_keras(val_y)
            fit_params["validation_data"] = (val_X, val_y_onehot)
        elif "validation_split" in kwargs:
            fit_params["validation_split"] = kwargs["validation_split"]

        self.history = self.model_.fit(X, y_onehot, **fit_params)
        return self
    
    def predict(self, X):
        """Make predictions on new data."""
        if self.model_ is None:
            raise ValueError("Model has not been fitted yet.")
        
        y_pred_proba = self.model_.predict(X)
        y_pred_int = np.argmax(y_pred_proba, axis=1)
        y_pred = self.label_encoder.inverse_transform(y_pred_int)
        return y_pred
    
    def predict_proba(self, X):
        """Predict class probabilities."""
        if self.model_ is None:
            raise ValueError("Model has not been fitted yet.")
        return self.model_.predict(X)
    
    def build_model(self, input_shape, n_classes):
        """Build the model architecture. To be implemented by subclasses."""
        raise NotImplementedError("Subclasses must implement build_model method")

print("✅ BaseTimeSeriesClassifier defined successfully!")

# %% [markdown]
# ## 4. ResNet Model Implementation

# %%
class ResNetClassifier(BaseTimeSeriesClassifier):
    """ResNet for time series classification."""
    
    def __init__(self, n_residual_blocks=3, n_filters=64, **kwargs):
        super().__init__(**kwargs)
        self.n_residual_blocks = n_residual_blocks
        self.n_filters = n_filters
        
        if self.optimizer is None:
            self.optimizer = keras.optimizers.Adam(learning_rate=0.001)
        if self.metrics is None:
            self.metrics = ["accuracy"]

    def _residual_block(self, x, filters, kernel_size=8, stride=1):
        """Create a residual block."""
        shortcut = x
        
        # First conv layer
        x = keras.layers.Conv1D(
            filters=filters,
            kernel_size=kernel_size,
            strides=stride,
            padding='same'
        )(x)
        x = keras.layers.BatchNormalization()(x)
        x = keras.layers.ReLU()(x)
        
        # Second conv layer
        x = keras.layers.Conv1D(
            filters=filters,
            kernel_size=kernel_size,
            strides=1,
            padding='same'
        )(x)
        x = keras.layers.BatchNormalization()(x)
        
        # Adjust shortcut if needed
        if stride != 1 or shortcut.shape[-1] != filters:
            shortcut = keras.layers.Conv1D(
                filters=filters,
                kernel_size=1,
                strides=stride,
                padding='same'
            )(shortcut)
            shortcut = keras.layers.BatchNormalization()(shortcut)
        
        # Add shortcut connection
        x = keras.layers.Add()([x, shortcut])
        x = keras.layers.ReLU()(x)
        
        return x

    def build_model(self, input_shape, n_classes):
        """Build ResNet model."""
        tf.random.set_seed(self.random_state)
        
        input_layer = keras.layers.Input(shape=input_shape)
        
        # Initial conv layer
        x = keras.layers.Conv1D(
            filters=self.n_filters,
            kernel_size=8,
            padding='same'
        )(input_layer)
        x = keras.layers.BatchNormalization()(x)
        x = keras.layers.ReLU()(x)
        
        # Residual blocks
        for i in range(self.n_residual_blocks):
            filters = self.n_filters * (2 ** i)
            stride = 2 if i > 0 else 1
            x = self._residual_block(x, filters, stride=stride)
        
        # Global average pooling
        x = keras.layers.GlobalAveragePooling1D()(x)
        x = keras.layers.Dropout(0.5)(x)
        
        # Output layer
        output_layer = keras.layers.Dense(
            units=n_classes,
            activation=self.activation,
            use_bias=self.use_bias
        )(x)
        
        model = keras.models.Model(inputs=input_layer, outputs=output_layer)
        model.compile(
            loss=self.loss,
            optimizer=self.optimizer,
            metrics=self.metrics
        )
        
        return model

print("✅ ResNetClassifier defined successfully!")

# %% [markdown]
# ## 5. 1D CNN Model Implementation

# %%
class CNN1DClassifier(BaseTimeSeriesClassifier):
    """1D CNN for time series classification."""
    
    def __init__(self, n_filters=128, kernel_size=3, dropout_rate=0.5, **kwargs):
        super().__init__(**kwargs)
        self.n_filters = n_filters
        self.kernel_size = kernel_size
        self.dropout_rate = dropout_rate
        
        if self.optimizer is None:
            self.optimizer = keras.optimizers.Adam(learning_rate=0.001)
        if self.metrics is None:
            self.metrics = ["accuracy"]

    def build_model(self, input_shape, n_classes):
        """Build 1D CNN model."""
        tf.random.set_seed(self.random_state)
        
        input_layer = keras.layers.Input(shape=input_shape)
        
        # First Conv1D block
        x = keras.layers.Conv1D(
            filters=self.n_filters,
            kernel_size=self.kernel_size,
            padding='same',
            activation='relu'
        )(input_layer)
        x = keras.layers.BatchNormalization()(x)
        x = keras.layers.MaxPooling1D(pool_size=2)(x)
        x = keras.layers.Dropout(self.dropout_rate)(x)
        
        # Global pooling to flatten
        x = keras.layers.GlobalAveragePooling1D()(x)
        
        # Output layer
        output_layer = keras.layers.Dense(
            units=n_classes,
            activation=self.activation,
            use_bias=self.use_bias
        )(x)
        
        model = keras.models.Model(inputs=input_layer, outputs=output_layer)
        model.compile(
            loss=self.loss,
            optimizer=self.optimizer,
            metrics=self.metrics
        )
        
        return model

print("✅ CNN1DClassifier defined successfully!")

# %% [markdown]
# ## 6. LSTM Model Implementation

# %%
class LSTMClassifier(BaseTimeSeriesClassifier):
    """LSTM for time series classification."""
    
    def __init__(self, n_units=512, dropout_rate=0.5, recurrent_dropout=0.5, **kwargs):
        super().__init__(**kwargs)
        self.n_units = n_units
        self.dropout_rate = dropout_rate
        self.recurrent_dropout = recurrent_dropout
        
        if self.optimizer is None:
            self.optimizer = keras.optimizers.Adam(learning_rate=0.001)
        if self.metrics is None:
            self.metrics = ["accuracy"]

    def build_model(self, input_shape, n_classes):
        """Build LSTM model."""
        tf.random.set_seed(self.random_state)
        
        input_layer = keras.layers.Input(shape=input_shape)
        
        # First LSTM layer
        x = keras.layers.LSTM(
            units=self.n_units,
            return_sequences=True,
            dropout=self.dropout_rate,
            recurrent_dropout=self.recurrent_dropout
        )(input_layer)
        
        # Second LSTM layer (final)
        x = keras.layers.LSTM(
            units=self.n_units,
            dropout=self.dropout_rate,
            recurrent_dropout=self.recurrent_dropout
        )(x)
        
        # Output layer
        output_layer = keras.layers.Dense(
            units=n_classes,
            activation=self.activation,
            use_bias=self.use_bias
        )(x)
        
        model = keras.models.Model(inputs=input_layer, outputs=output_layer)
        model.compile(
            loss=self.loss,
            optimizer=self.optimizer,
            metrics=self.metrics
        )
        
        return model

print("✅ LSTMClassifier defined successfully!")

# %% [markdown]
# ## 7. CNN-LSTM Hybrid Model Implementation

# %%
class CNNLSTMClassifier(BaseTimeSeriesClassifier):
    """CNN-LSTM hybrid for time series classification."""
    
    def __init__(self, n_filters=64, kernel_size=8, n_units=64, 
                 dropout_rate=0.5, recurrent_dropout=0.5, **kwargs):
        super().__init__(**kwargs)
        self.n_filters = n_filters
        self.kernel_size = kernel_size
        self.n_units = n_units
        self.dropout_rate = dropout_rate
        self.recurrent_dropout = recurrent_dropout
        
        if self.optimizer is None:
            self.optimizer = keras.optimizers.Adam(learning_rate=0.001)
        if self.metrics is None:
            self.metrics = ["accuracy"]

    def build_model(self, input_shape, n_classes):
        """Build CNN-LSTM hybrid model."""
        tf.random.set_seed(self.random_state)
        
        input_layer = keras.layers.Input(shape=input_shape)
        
        # CNN Feature Extraction
        # CNN Feature Extraction (1 layer)
        x = keras.layers.Conv1D(
            filters=self.n_filters,
            kernel_size=self.kernel_size,
            padding='same',
            activation='relu'
        )(input_layer)
        x = keras.layers.BatchNormalization()(x)
        x = keras.layers.MaxPooling1D(pool_size=2)(x)
        
        # LSTM Sequence Processing (2 layers)
        # First LSTM layer
        x = keras.layers.LSTM(
            units=self.n_units,
            return_sequences=True,
            dropout=self.dropout_rate,
            recurrent_dropout=self.recurrent_dropout
        )(x)
        
        # Second LSTM layer (final)
        x = keras.layers.LSTM(
            units=self.n_units,
            dropout=self.dropout_rate,
            recurrent_dropout=self.recurrent_dropout
        )(x)
        
        # Output layer
        output_layer = keras.layers.Dense(
            units=n_classes,
            activation=self.activation,
            use_bias=self.use_bias
        )(x)
        
        model = keras.models.Model(inputs=input_layer, outputs=output_layer)
        model.compile(
            loss=self.loss,
            optimizer=self.optimizer,
            metrics=self.metrics
        )
        
        return model

print("✅ CNNLSTMClassifier defined successfully!")

# %% [markdown]
# ## 8. Model Comparison Functions

# %%
def compare_models(XXX_train, y_train, XXX_valid, y_valid, n_epochs=100, verbose=False):
    """Compare all four models on the same dataset."""
    
    # Check if labels are continuous and convert to discrete if needed
    from sklearn.utils.multiclass import type_of_target
    
    y_train_type = type_of_target(y_train)
    y_valid_type = type_of_target(y_valid)
    
    print(f"Original y_train type: {y_train_type}")
    print(f"Original y_valid type: {y_valid_type}")
    
    if y_train_type == 'continuous' or y_valid_type == 'continuous':
        print("⚠️  Warning: Labels are continuous. Converting to discrete classes...")
        
        # Convert continuous labels to discrete classes
        # Assuming labels represent different classes, we'll use binning or rounding
        if hasattr(y_train, 'dtype') and np.issubdtype(y_train.dtype, np.floating):
            # If floating point, round to nearest integer
            y_train = np.round(y_train).astype(int)
            y_valid = np.round(y_valid).astype(int)
        
        # Ensure labels start from 0
        unique_labels = np.unique(np.concatenate([y_train, y_valid]))
        label_mapping = {old_label: new_label for new_label, old_label in enumerate(unique_labels)}
        
        y_train = np.array([label_mapping[label] for label in y_train])
        y_valid = np.array([label_mapping[label] for label in y_valid])
        
        print(f"Converted labels - unique values: {np.unique(y_train)}")
        print(f"Number of classes: {len(np.unique(y_train))}")
    
    # Initialize models
    models = {
        'ResNet': ResNetClassifier(
            n_epochs=n_epochs,
            batch_size=OPTIMAL_BATCH_SIZE,
            verbose=verbose,
            random_state=42
        ),
        '1D CNN': CNN1DClassifier(
            n_epochs=n_epochs,
            batch_size=OPTIMAL_BATCH_SIZE,
            verbose=verbose,
            random_state=42,
            n_filters=64,
            kernel_size=8
        ),
        'LSTM': LSTMClassifier(
            n_epochs=n_epochs,
            batch_size=OPTIMAL_BATCH_SIZE,
            verbose=verbose,
            random_state=42,
            n_units=64
        ),
        'CNN-LSTM': CNNLSTMClassifier(
            n_epochs=n_epochs,
            batch_size=OPTIMAL_BATCH_SIZE,
            verbose=verbose,
            random_state=42,
            n_filters=64,
            n_units=64
        )
    }
    
    results = {}
    
    print("=" * 60)
    print("MODEL COMPARISON RESULTS")
    print("=" * 60)
    
    for model_name, model in models.items():
        print(f"\n🔄 Training {model_name}...")
        
        # Add t-SNE callback for visualization
        tsne_callback = TSNECallback(
            model_name=model_name.lower().replace(' ', '_').replace('-', '_'),
            XXX_valid=XXX_valid,
            y_valid=y_valid,
            target_epochs=[20, 40, 60] if n_epochs >= 60 else [20, 40] if n_epochs >= 40 else [20] if n_epochs >= 20 else [n_epochs]
        )
        
        model.callbacks = [tsne_callback]
        
        # Train model and measure time
        start_time = time.time()
        model.fit(XXX_train, y_train, validation_data=(XXX_valid, y_valid))
        training_time = time.time() - start_time
        
        # Make predictions
        y_pred_train = model.predict(XXX_train)
        y_pred_valid = model.predict(XXX_valid)
        
        # Debug: Check data types and shapes
        print(f"\nDEBUG - {model_name}:")
        print(f"y_train type: {type(y_train)}, shape: {y_train.shape if hasattr(y_train, 'shape') else 'N/A'}")
        print(f"y_train sample: {y_train[:5] if hasattr(y_train, '__getitem__') else 'N/A'}")
        print(f"y_pred_train type: {type(y_pred_train)}, shape: {y_pred_train.shape if hasattr(y_pred_train, 'shape') else 'N/A'}")
        print(f"y_pred_train sample: {y_pred_train[:5] if hasattr(y_pred_train, '__getitem__') else 'N/A'}")
        print(f"y_train unique values: {np.unique(y_train) if hasattr(y_train, '__iter__') else 'N/A'}")
        print(f"y_pred_train unique values: {np.unique(y_pred_train) if hasattr(y_pred_train, '__iter__') else 'N/A'}")
        
        # Calculate metrics
        train_acc = accuracy_score(y_train, y_pred_train)
        valid_acc = accuracy_score(y_valid, y_pred_valid)
        
        # Get detailed metrics for validation set
        precision, recall, f1, _ = precision_recall_fscore_support(
            y_valid, y_pred_valid, average='weighted'
        )
        
        # Get per-class F1 scores for both train and validation
        f1_train_per_class = f1_score(y_train, y_pred_train, average=None)
        f1_valid_per_class = f1_score(y_valid, y_pred_valid, average=None)
        
        # Get classification reports with all labels
        train_report = classification_report(y_train, y_pred_train, output_dict=True)
        valid_report = classification_report(y_valid, y_pred_valid, output_dict=True)
        
        # Generate confusion matrices
        cm_train = confusion_matrix(y_train, y_pred_train)
        cm_valid = confusion_matrix(y_valid, y_pred_valid)
        
        # Display confusion matrices
        fig, axes = plt.subplots(1, 2, figsize=(15, 6))
        
        # Training confusion matrix
        disp_train = ConfusionMatrixDisplay(confusion_matrix=cm_train)
        disp_train.plot(ax=axes[0], cmap='Blues')
        axes[0].set_title(f'{model_name} - Training Set Confusion Matrix')
        
        # Validation confusion matrix
        disp_valid = ConfusionMatrixDisplay(confusion_matrix=cm_valid)
        disp_valid.plot(ax=axes[1], cmap='Blues')
        axes[1].set_title(f'{model_name} - Validation Set Confusion Matrix')
        
        plt.tight_layout()
        
        # Save the confusion matrix plot
        plot_filename = f"{model_name.lower().replace(' ', '_').replace('-', '_')}_confusion_matrices.png"
        plt.savefig(plot_filename, dpi=300, bbox_inches='tight')
        print(f"💾 Confusion matrices saved as: {plot_filename}")
        
        plt.show()
        
        # Display detailed F1 scores
        print(f"\n📊 Detailed F1-Scores for {model_name}:")
        print("=" * 50)
        
        # Create DataFrame for F1 scores by class
        class_names = [f"Class {i}" for i in range(len(f1_train_per_class))]
        f1_df = pd.DataFrame({
            'Class': class_names,
            'F1_Train': f1_train_per_class,
            'F1_Valid': f1_valid_per_class
        })
        f1_df['F1_Difference'] = f1_df['F1_Valid'] - f1_df['F1_Train']
        
        print("Per-Class F1 Scores:")
        display(f1_df.round(4))
        
        # Display full classification reports
        print(f"\n📋 Full Classification Report - Training Set:")
        train_report_df = pd.DataFrame(train_report).transpose()
        display(train_report_df.round(4))
        
        print(f"\n📋 Full Classification Report - Validation Set:")
        valid_report_df = pd.DataFrame(valid_report).transpose()
        display(valid_report_df.round(4))
        
        # Store results
        results[model_name] = {
            'model': model,
            'train_accuracy': train_acc,
            'valid_accuracy': valid_acc,
            'precision': precision,
            'recall': recall,
            'f1_score': f1,
            'f1_train_per_class': f1_train_per_class,
            'f1_valid_per_class': f1_valid_per_class,
            'train_report': train_report,
            'valid_report': valid_report,
            'confusion_matrix_train': cm_train,
            'confusion_matrix_valid': cm_valid,
            'training_time': training_time,
            'y_pred_train': y_pred_train,
            'y_pred_valid': y_pred_valid,
            'history': model.history
        }
        
        # Save model
        model_filename = f"{model_name.lower().replace(' ', '_').replace('-', '_')}_model.keras"
        model.model_.save(model_filename)
        print(f"💾 Model saved as: {model_filename}")
        
        print(f"✅ {model_name} completed!")
        print(f"   Training Accuracy: {train_acc:.4f}")
        print(f"   Validation Accuracy: {valid_acc:.4f}")
        print(f"   F1-Score: {f1:.4f}")
        print(f"   Training Time: {training_time:.2f}s")
    
    return results

print("✅ compare_models function defined successfully!")

# %%
def plot_comparison_results(results):
    """Plot comparison results."""
    
    # Extract metrics for plotting
    model_names = list(results.keys())
    train_accs = [results[name]['train_accuracy'] for name in model_names]
    valid_accs = [results[name]['valid_accuracy'] for name in model_names]
    f1_scores = [results[name]['f1_score'] for name in model_names]
    training_times = [results[name]['training_time'] for name in model_names]
    
    # Create subplots
    fig, axes = plt.subplots(2, 2, figsize=(15, 12))
    fig.suptitle('Model Comparison Results', fontsize=16, fontweight='bold')
    
    # 1. Accuracy Comparison
    x = np.arange(len(model_names))
    width = 0.35
    
    axes[0, 0].bar(x - width/2, train_accs, width, label='Training', alpha=0.8)
    axes[0, 0].bar(x + width/2, valid_accs, width, label='Validation', alpha=0.8)
    axes[0, 0].set_xlabel('Models')
    axes[0, 0].set_ylabel('Accuracy')
    axes[0, 0].set_title('Training vs Validation Accuracy')
    axes[0, 0].set_xticks(x)
    axes[0, 0].set_xticklabels(model_names)
    axes[0, 0].legend()
    axes[0, 0].grid(True, alpha=0.3)
    
    # Add value labels on bars
    for i, (train_acc, valid_acc) in enumerate(zip(train_accs, valid_accs)):
        axes[0, 0].text(i - width/2, train_acc + 0.01, f'{train_acc:.3f}', 
                       ha='center', va='bottom', fontsize=9)
        axes[0, 0].text(i + width/2, valid_acc + 0.01, f'{valid_acc:.3f}', 
                       ha='center', va='bottom', fontsize=9)
    
    # 2. F1-Score Comparison
    bars = axes[0, 1].bar(model_names, f1_scores, color='skyblue', alpha=0.8)
    axes[0, 1].set_xlabel('Models')
    axes[0, 1].set_ylabel('F1-Score')
    axes[0, 1].set_title('F1-Score Comparison')
    axes[0, 1].grid(True, alpha=0.3)
    
    # Add value labels
    for bar, f1 in zip(bars, f1_scores):
        axes[0, 1].text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.01,
                       f'{f1:.3f}', ha='center', va='bottom', fontsize=9)
    
    # 3. Training Time Comparison
    bars = axes[1, 0].bar(model_names, training_times, color='lightcoral', alpha=0.8)
    axes[1, 0].set_xlabel('Models')
    axes[1, 0].set_ylabel('Training Time (seconds)')
    axes[1, 0].set_title('Training Time Comparison')
    axes[1, 0].grid(True, alpha=0.3)
    
    # Add value labels
    for bar, time_val in zip(bars, training_times):
        axes[1, 0].text(bar.get_x() + bar.get_width()/2, bar.get_height() + 1,
                       f'{time_val:.1f}s', ha='center', va='bottom', fontsize=9)
    
    # 4. Overall Performance Radar Chart
    metrics = ['Valid Acc', 'F1-Score', 'Speed*']
    # Normalize speed (inverse of time, scaled)
    max_time = max(training_times)
    speed_scores = [(max_time - t) / max_time for t in training_times]
    
    angles = np.linspace(0, 2 * np.pi, len(metrics), endpoint=False).tolist()
    angles += angles[:1]  # Complete the circle
    
    axes[1, 1].remove()  # Remove the subplot
    ax_radar = fig.add_subplot(2, 2, 4, projection='polar')
    
    colors = ['blue', 'red', 'green', 'orange']
    for i, model_name in enumerate(model_names):
        values = [valid_accs[i], f1_scores[i], speed_scores[i]]
        values += values[:1]  # Complete the circle
        
        ax_radar.plot(angles, values, 'o-', linewidth=2, 
                     label=model_name, color=colors[i])
        ax_radar.fill(angles, values, alpha=0.25, color=colors[i])
    
    ax_radar.set_xticks(angles[:-1])
    ax_radar.set_xticklabels(metrics)
    ax_radar.set_ylim(0, 1)
    ax_radar.set_title('Overall Performance Comparison', y=1.08)
    ax_radar.legend(loc='upper right', bbox_to_anchor=(1.3, 1.0))
    
    plt.tight_layout()
    
    # Save the comparison plot
    plt.savefig('model_comparison_results.png', dpi=300, bbox_inches='tight')
    print("💾 Comparison results saved as: model_comparison_results.png")
    
    plt.show()

print("✅ plot_comparison_results function defined successfully!")

# %%
def create_summary_table(results):
    """Create a summary table of all results."""
    
    summary_data = []
    for model_name, result in results.items():
        summary_data.append({
            'Model': model_name,
            'Train Accuracy': f"{result['train_accuracy']:.4f}",
            'Valid Accuracy': f"{result['valid_accuracy']:.4f}",
            'Precision': f"{result['precision']:.4f}",
            'Recall': f"{result['recall']:.4f}",
            'F1-Score': f"{result['f1_score']:.4f}",
            'Training Time (s)': f"{result['training_time']:.2f}"
        })
    
    df_summary = pd.DataFrame(summary_data)
    
    print("\n" + "="*80)
    print("SUMMARY TABLE")
    print("="*80)
    display(df_summary)
    
    return df_summary

print("✅ create_summary_table function defined successfully!")

# %%
def plot_training_history(results):
    """Plot training history for all models."""
    
    fig, axes = plt.subplots(2, 2, figsize=(15, 10))
    fig.suptitle('Training History Comparison', fontsize=16, fontweight='bold')
    
    colors = ['blue', 'red', 'green', 'orange']
    
    # Plot training loss
    for i, (model_name, result) in enumerate(results.items()):
        if result['history'] is not None:
            history = result['history'].history
            epochs = range(1, len(history['loss']) + 1)
            
            axes[0, 0].plot(epochs, history['loss'], 
                           color=colors[i], label=f'{model_name}', linewidth=2)
    
    axes[0, 0].set_xlabel('Epochs')
    axes[0, 0].set_ylabel('Loss')
    axes[0, 0].set_title('Training Loss')
    axes[0, 0].legend()
    axes[0, 0].grid(True, alpha=0.3)
    
    # Plot validation loss
    for i, (model_name, result) in enumerate(results.items()):
        if result['history'] is not None:
            history = result['history'].history
            if 'val_loss' in history:
                epochs = range(1, len(history['val_loss']) + 1)
                axes[0, 1].plot(epochs, history['val_loss'], 
                               color=colors[i], label=f'{model_name}', linewidth=2)
    
    axes[0, 1].set_xlabel('Epochs')
    axes[0, 1].set_ylabel('Validation Loss')
    axes[0, 1].set_title('Validation Loss')
    axes[0, 1].legend()
    axes[0, 1].grid(True, alpha=0.3)
    
    # Plot training accuracy
    for i, (model_name, result) in enumerate(results.items()):
        if result['history'] is not None:
            history = result['history'].history
            if 'accuracy' in history:
                epochs = range(1, len(history['accuracy']) + 1)
                axes[1, 0].plot(epochs, history['accuracy'], 
                               color=colors[i], label=f'{model_name}', linewidth=2)
    
    axes[1, 0].set_xlabel('Epochs')
    axes[1, 0].set_ylabel('Training Accuracy')
    axes[1, 0].set_title('Training Accuracy')
    axes[1, 0].legend()
    axes[1, 0].grid(True, alpha=0.3)
    
    # Plot validation accuracy
    for i, (model_name, result) in enumerate(results.items()):
        if result['history'] is not None:
            history = result['history'].history
            if 'val_accuracy' in history:
                epochs = range(1, len(history['val_accuracy']) + 1)
                axes[1, 1].plot(epochs, history['val_accuracy'], 
                               color=colors[i], label=f'{model_name}', linewidth=2)
    
    axes[1, 1].set_xlabel('Epochs')
    axes[1, 1].set_ylabel('Validation Accuracy')
    axes[1, 1].set_title('Validation Accuracy')
    axes[1, 1].legend()
    axes[1, 1].grid(True, alpha=0.3)
    
    plt.tight_layout()
    
    # Save the training history plot
    plt.savefig('training_history_comparison.png', dpi=300, bbox_inches='tight')
    print("💾 Training history saved as: training_history_comparison.png")
    
    plt.show()

print("✅ plot_training_history function defined successfully!")

# %% [markdown]
# ## 9. Complete Comparison Function

# %%
def run_complete_comparison(XXX_train, y_train, XXX_valid, y_valid, n_epochs=100):
    """Run complete model comparison with all visualizations."""
    
    print("🚀 Starting comprehensive model comparison...")
    print(f"Training data shape: {XXX_train.shape}")
    print(f"Validation data shape: {XXX_valid.shape}")
    print(f"Number of classes: {len(np.unique(y_train))}")
    print(f"Training epochs: {n_epochs}")
    
    # Run comparison
    results = compare_models(XXX_train, y_train, XXX_valid, y_valid, n_epochs)
    
    # Create visualizations
    plot_comparison_results(results)
    plot_training_history(results)
    
    # Create summary table
    summary_df = create_summary_table(results)
    
    # Model ranking
    print("\n" + "="*80)
    print("🏆 FINAL MODEL RANKING 🏆")
    print("="*80)
    
    model_accuracies = {
        name: result['valid_accuracy'] 
        for name, result in results.items()
    }
    
    sorted_models = sorted(model_accuracies.items(), key=lambda x: x[1], reverse=True)
    
    print("Ranking by Validation Accuracy:")
    for i, (model_name, accuracy) in enumerate(sorted_models, 1):
        print(f"{i}. {model_name}: {accuracy:.4f}")
    
    print(f"\n🥇 Best Model: {sorted_models[0][0]} with {sorted_models[0][1]:.4f} accuracy")
    
    print("\n🎉 Model comparison completed!")
    
    return results, summary_df

print("✅ run_complete_comparison function defined successfully!")

# %% [markdown]
# ## 10. Individual Model Testing Section

# %% [markdown]
# ### Load Your Data Here
# Replace this section with your actual data loading code from your original notebook.

# %%
import os
from scipy.io import loadmat
from scipy.interpolate import interp1d
directory = 'data'

all_data = {}

# Iterate over all files in the directory
for filename in os.listdir(directory):
    if filename.endswith('.mat'):
        filepath = os.path.join(directory, filename)
        # Load the .mat file and add its contents to the dictionary
        mat_data = loadmat(filepath)
        
        # Use filename (without extension) as key for the data
        key = os.path.splitext(filename)[0]
        all_data[key] = mat_data['acceleration']

# %%
keys_to_stack = [f'NamO{i}' for i in range(11)]
input_data = np.stack([all_data[key] for key in keys_to_stack], axis=0)

# Create the corresponding labels
output_labels = np.linspace(0,28,11)  # Using 0 and 1 as class labels for binary cross-entropy
labels = output_labels

input_data = input_data[:,:,:40000]
input_data.shape, output_labels.shape

# %%
# Select the data at index (1, 1, :) which has a shape of (50000,)
Data = input_data[5, :, :]
print(Data.shape)
# Create the plot
fig, axes = plt.subplots(input_data.shape[1]-18, 1, figsize=(15, 8), sharex=True)

title_font = {'family': 'Times New Roman', 'size': 16, 'weight': 'bold'}
label_font = {'family': 'Times New Roman', 'size': 14}
plt.rcParams['xtick.labelsize'] = 10
plt.rcParams['ytick.labelsize'] = 10
plt.rcParams['font.family'] = 'sans-serif'
#print(axes)
# Plot the data for each sub-array
for i, ax in enumerate(axes):
    ax.plot(Data[i, :], linewidth=1, color = 'b')
    # ax.set_title(f'Z24 Signal Data at Index (1, {i}, :)', fontsize=12)
    
    ax.grid(True, which='both', linestyle='--', linewidth=0.5)
    ax.minorticks_on()
    ax.grid(True, which='minor', color='#999999', linestyle='--', alpha=0.2)
    ax.set_xlim(-100, Data.shape[1]+100)
# Set common labels using axes
axes[-1].set_xlabel('Time Points', fontsize=14, fontdict=label_font)
axes[0].set_title('NamO Signal', fontsize=16, fontdict=title_font)

# Create a "super" axis for the common Y-label and make it invisible
super_ax = fig.add_subplot(111, frame_on=False)
plt.tick_params(labelcolor="none", bottom=False, left=False)
super_ax.set_ylabel("Amplitude", fontsize=14, labelpad=15, fontdict=label_font)

# Move the super axis ylabel to avoid overlap with subplots
super_ax.yaxis.set_label_coords(-0.06,0.5)

# Adjust the layout so that plots do not overlap
plt.tight_layout()
plt.show()

# %%
import numpy as np
import random

def augment_time_series_data(input_data, labels, num_augmentations=4):
    """
    Augment time series data.

    :param input_data: Original time series data array.
    :param labels: Corresponding labels for the data.
    :param num_augmentations: Number of augmented samples to generate per original sample.

    :return: Augmented data array and corresponding labels.
    """
    augmented_data = []
    augmented_labels = []

    num_samples, num_channels, sequence_length = input_data.shape
    #print (sequence_length)

    for i in range(num_samples):
        for _ in range(num_augmentations):
            # Choose a random augmentation technique
            augmentation_type = random.choice(['noise', 'reverse', 'crop_pad'])

            if augmentation_type == 'noise':
                # Add random noise
                noise = np.random.normal(0, 0.001, input_data[i].shape)
                augmented_sample = input_data[i] + noise

            elif augmentation_type == 'reverse':
                # Reverse the sequence
                augmented_sample = np.flip(input_data[i], axis=-1)

            elif augmentation_type == 'crop_pad':
                # Crop and pad the sequence
                crop_size = random.randint(0, sequence_length // 100)
                padded_sample = np.pad(input_data[i], ((0, 0), (crop_size, 0)), mode='constant', constant_values=0)
                augmented_sample = padded_sample[:, :-crop_size]

            augmented_data.append(augmented_sample)
            augmented_labels.append(labels[i])

    # Convert to numpy arrays
    augmented_data = np.array(augmented_data)
    augmented_labels = np.array(augmented_labels)

    return augmented_data, augmented_labels

# Sử dụng hàm
augmented_data, augmented_labels = augment_time_series_data(input_data, output_labels)
print(augmented_data.shape, augmented_labels.shape)

# %%
# Select the data at index (1, 1, :) which has a shape of (50000,)
Data = augmented_data[29, :, :]
print(Data.shape)
# Create the plot
fig, axes = plt.subplots(augmented_data.shape[1]-18, 1, figsize=(15, 8), sharex=True)

title_font = {'family': 'Times New Roman', 'size': 16, 'weight': 'bold'}
label_font = {'family': 'Times New Roman', 'size': 14}
plt.rcParams['xtick.labelsize'] = 10
plt.rcParams['ytick.labelsize'] = 10
plt.rcParams['font.family'] = 'sans-serif'
#print(axes)
# Plot the data for each sub-array
for i, ax in enumerate(axes):
    ax.plot(Data[i, :], linewidth=1, color = 'b')
    # ax.set_title(f'Z24 Signal Data at Index (1, {i}, :)', fontsize=12)
    
    ax.grid(True, which='both', linestyle='--', linewidth=0.5)
    ax.minorticks_on()
    ax.grid(True, which='minor', color='#999999', linestyle='--', alpha=0.2)
    ax.set_xlim(-100, Data.shape[1]+100)
# Set common labels using axes
axes[-1].set_xlabel('Time Points', fontsize=14, fontdict=label_font)
axes[0].set_title('NamO Signal', fontsize=16, fontdict=title_font)

# Create a "super" axis for the common Y-label and make it invisible
super_ax = fig.add_subplot(111, frame_on=False)
plt.tick_params(labelcolor="none", bottom=False, left=False)
super_ax.set_ylabel("Amplitude", fontsize=14, labelpad=15, fontdict=label_font)

# Move the super axis ylabel to avoid overlap with subplots
super_ax.yaxis.set_label_coords(-0.06,0.5)

# Adjust the layout so that plots do not overlap
plt.tight_layout()
plt.show()

# %%
import numpy as np

def reshape_time_series_data_v8(input_data, label_data, segments_per_new_sample, segment_length):
    """
    Reshape time series data and corresponding labels into a specified shape.

    :param input_data: Original time series data array.
    :param label_data: Corresponding labels for the data.
    :param segments_per_new_sample: Number of segments per new sample.
    :param segment_length: Length of each segment.

    :return: Reshaped data array and corresponding labels.
    """
    num_samples_original, num_channels, length_original = input_data.shape

    # Validate the feasibility of reshaping
    if length_original % segment_length != 0:
        raise ValueError("Segment length must evenly divide the original length.")

    total_segments_per_original_sample = (length_original // segment_length) * num_channels
    num_samples_new = (num_samples_original * total_segments_per_original_sample) // segments_per_new_sample

    # Validate if reshaping is possible
    if (num_samples_original * total_segments_per_original_sample) % segments_per_new_sample != 0:
        raise ValueError("Reshaping not possible with the given dimensions.")

    # Initialize reshaped data and labels
    new_shape = (num_samples_new, segments_per_new_sample, segment_length)
    reshaped_data = np.zeros(new_shape)
    reshaped_labels = np.zeros(num_samples_new)

    # Reshape the data and labels
    count = 0
    for i in range(num_samples_original):
        segment_count = 0
        for j in range(num_channels):
            for k in range(length_original // segment_length):
                start_idx = k * segment_length
                end_idx = start_idx + segment_length
                reshaped_data[count, segment_count % segments_per_new_sample, :] = input_data[i, j, start_idx:end_idx]
                if (segment_count + 1) % segments_per_new_sample == 0:
                    reshaped_labels[count] = label_data[i]  # Assign corresponding label
                    count += 1
                segment_count += 1

    return reshaped_data, reshaped_labels

# Example usage
segments_per_new_sample = 10
segment_length = 4000

# Assume 'augmented_data' and 'augmented_labels' are your input data and labels
reshaped_data, reshaped_labels = reshape_time_series_data_v8(augmented_data, augmented_labels, segments_per_new_sample, segment_length)
print(reshaped_data.shape, reshaped_labels.shape)


# %%
# Select the data at index (1, 1, :) which has a shape of (8000,)
Data = reshaped_data[1, :, :] #150 -> 200
print(Data.shape)
# Create the plot
fig, axes = plt.subplots(reshaped_data.shape[1], 1, figsize=(15, 8), sharex=True)

title_font = {'family': 'Times New Roman', 'size': 16, 'weight': 'bold'}
label_font = {'family': 'Times New Roman', 'size': 14}
plt.rcParams['xtick.labelsize'] = 10
plt.rcParams['ytick.labelsize'] = 10
plt.rcParams['font.family'] = 'sans-serif'

# Plot the data for each sub-array
for i, ax in enumerate(axes):
    ax.plot(Data[i, :], linewidth=1, color = 'b')
    # ax.set_title(f'Z24 Signal Data at Index (1, {i}, :)', fontsize=12)
    
    ax.grid(True, which='both', linestyle='--', linewidth=0.5)
    ax.minorticks_on()
    ax.grid(True, which='minor', color='#999999', linestyle='--', alpha=0.2)
    ax.set_xlim(-100, Data.shape[1]+100)
# Set common labels using axes
axes[-1].set_xlabel('Time Points', fontsize=14, fontdict=label_font)
axes[0].set_title('NamO Signal', fontsize=16, fontdict=title_font)

# Create a "super" axis for the common Y-label and make it invisible
super_ax = fig.add_subplot(111, frame_on=False)
plt.tick_params(labelcolor="none", bottom=False, left=False)
super_ax.set_ylabel("Amplitude", fontsize=14, labelpad=15, fontdict=label_font)

# Move the super axis ylabel to avoid overlap with subplots
super_ax.yaxis.set_label_coords(-0.06,0.5)

# Adjust the layout so that plots do not overlap
plt.tight_layout()
plt.show()

# %%
from sklearn.model_selection import train_test_split
# Assuming reshaped_data and reshaped_labels are defined
input_train = reshaped_data  # Original shape is (924, 10, 5000)
output_train = reshaped_labels

# Split the data into training and validation sets
XXX_train_reshaped, XXX_valid_reshaped, y_train, y_valid = train_test_split(input_train, output_train, test_size=0.2, random_state=42)

# Now, reshape XXX_train and XXX_valid from (N, 10, 5000) to (N, 5000, 10)
XXX_train = XXX_train_reshaped.reshape(XXX_train_reshaped.shape[0], 10, 4000)
XXX_valid = XXX_valid_reshaped.reshape(XXX_valid_reshaped.shape[0], 10, 4000)

# Check and fix label types
from sklearn.utils.multiclass import type_of_target

print(f"Training data shape: {XXX_train.shape}")
print(f"Training labels shape: {y_train.shape}")
print(f"Validation data shape: {XXX_valid.shape}")
print(f"Validation labels shape: {y_valid.shape}")
print(f"Label type: {type_of_target(y_train)}")
print(f"Label data type: {y_train.dtype if hasattr(y_train, 'dtype') else type(y_train)}")
print(f"Sample labels: {y_train[:10]}")

# Convert labels to proper format if needed
if type_of_target(y_train) == 'continuous':
    print("⚠️  Converting continuous labels to discrete classes...")
    
    # Round to nearest integer if floating point
    if hasattr(y_train, 'dtype') and np.issubdtype(y_train.dtype, np.floating):
        y_train = np.round(y_train).astype(int)
        y_valid = np.round(y_valid).astype(int)
    
    # Ensure labels start from 0
    unique_labels = np.unique(np.concatenate([y_train, y_valid]))
    label_mapping = {old_label: new_label for new_label, old_label in enumerate(unique_labels)}
    
    y_train = np.array([label_mapping[label] for label in y_train])
    y_valid = np.array([label_mapping[label] for label in y_valid])
    
    print(f"✅ Labels converted. New type: {type_of_target(y_train)}")

print(f"Number of classes: {len(np.unique(y_train))}")
print(f"Class distribution: {np.bincount(y_train)}")

# %% [markdown]
# ### Run Complete Model Comparison

# %%
# ============================================================================
# COMPREHENSIVE MODEL COMPARISON
# ============================================================================

print("🔥 COMPREHENSIVE MODEL COMPARISON 🔥")
print("Comparing ResNet vs 1D CNN vs LSTM vs CNN-LSTM")

# Run the comparison (adjust n_epochs as needed)
results, summary_df = run_complete_comparison(
    XXX_train=XXX_train,
    y_train=y_train, 
    XXX_valid=XXX_valid,
    y_valid=y_valid,
    n_epochs=100  # Running with 100 epochs for comprehensive training
)

# Save comprehensive results for later analysis
import datetime
timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")

# Save main results
results_filename = f'model_comparison_results_{timestamp}.pkl'
with open(results_filename, 'wb') as f:
    pickle.dump(results, f)

# Save summary DataFrame
summary_filename = f'model_comparison_summary_{timestamp}.csv'
summary_df.to_csv(summary_filename, index=False)

# Save training data info for loading
data_info = {
    'X_train_shape': XXX_train.shape,
    'X_valid_shape': XXX_valid.shape,
    'y_train_shape': y_train.shape,
    'y_valid_shape': y_valid.shape,
    'n_classes': len(np.unique(y_train)),
    'class_names': [f'Class_{i}' for i in range(len(np.unique(y_train)))],
    'timestamp': timestamp,
    'n_epochs': 100
}

data_info_filename = f'training_data_info_{timestamp}.pkl'
with open(data_info_filename, 'wb') as f:
    pickle.dump(data_info, f)

# Create Final Training and Validation Loss Chart
print("\n📊 Creating Final Training and Validation Loss Chart...")

def create_final_loss_chart(results):
    """Create comprehensive final training and validation loss chart for all models."""
    
    fig, axes = plt.subplots(2, 2, figsize=(16, 12))
    fig.suptitle('Final Training and Validation Loss Comparison - All Models', 
                fontsize=16, fontweight='bold')
    
    model_names = list(results.keys())
    colors = ['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4']
    
    # Individual model loss curves
    for i, (model_name, color) in enumerate(zip(model_names, colors)):
        row = i // 2
        col = i % 2
        
        if 'history' in results[model_name] and results[model_name]['history']:
            history = results[model_name]['history'].history
            epochs = range(1, len(history['loss']) + 1)
            
            # Plot training loss
            axes[row, col].plot(epochs, history['loss'], 
                              color=color, linewidth=2, label='Training Loss')
            
            # Plot validation loss if available
            if 'val_loss' in history:
                axes[row, col].plot(epochs, history['val_loss'], 
                                  color=color, linewidth=2, linestyle='--', 
                                  label='Validation Loss', alpha=0.8)
            
            # Customize subplot
            axes[row, col].set_title(f'{model_name} Loss Curves', fontweight='bold')
            axes[row, col].set_xlabel('Epochs')
            axes[row, col].set_ylabel('Loss')
            axes[row, col].legend()
            axes[row, col].grid(True, alpha=0.3)
            
            # Add final loss values as text
            final_train_loss = history['loss'][-1]
            final_val_loss = history['val_loss'][-1] if 'val_loss' in history else None
            
            text_str = f'Final Train Loss: {final_train_loss:.4f}'
            if final_val_loss is not None:
                text_str += f'\\nFinal Val Loss: {final_val_loss:.4f}'
                text_str += f'\\nDifference: {abs(final_train_loss - final_val_loss):.4f}'
            
            axes[row, col].text(0.02, 0.98, text_str, 
                               transform=axes[row, col].transAxes,
                               verticalalignment='top',
                               bbox=dict(boxstyle='round', facecolor='white', alpha=0.8),
                               fontsize=9)
        else:
            axes[row, col].text(0.5, 0.5, f'No training history\\navailable for {model_name}',
                               ha='center', va='center', transform=axes[row, col].transAxes)
            axes[row, col].set_title(f'{model_name} - No Data')
    
    plt.tight_layout()
    
    # Save the final loss chart
    plt.savefig('final_training_validation_loss_chart.png', dpi=300, bbox_inches='tight')
    print("💾 Final loss chart saved as: final_training_validation_loss_chart.png")
    
    plt.show()
    
    # Create combined loss comparison
    plt.figure(figsize=(14, 8))
    
    # Plot all models on same chart for comparison
    for i, (model_name, color) in enumerate(zip(model_names, colors)):
        if 'history' in results[model_name] and results[model_name]['history']:
            history = results[model_name]['history'].history
            epochs = range(1, len(history['loss']) + 1)
            
            # Training loss
            plt.plot(epochs, history['loss'], color=color, linewidth=2, 
                    label=f'{model_name} (Train)', linestyle='-')
            
            # Validation loss
            if 'val_loss' in history:
                plt.plot(epochs, history['val_loss'], color=color, linewidth=2, 
                        label=f'{model_name} (Val)', linestyle='--', alpha=0.7)
    
    plt.title('All Models - Training and Validation Loss Comparison', 
             fontsize=14, fontweight='bold')
    plt.xlabel('Epochs')
    plt.ylabel('Loss')
    plt.legend(bbox_to_anchor=(1.05, 1), loc='upper left')
    plt.grid(True, alpha=0.3)
    plt.tight_layout()
    
    # Save the combined loss chart
    plt.savefig('combined_loss_comparison_chart.png', dpi=300, bbox_inches='tight')
    print("💾 Combined loss chart saved as: combined_loss_comparison_chart.png")
    
    plt.show()
    
    # Print loss summary statistics
    print("\n📊 FINAL LOSS SUMMARY:")
    print("=" * 60)
    
    loss_summary = []
    for model_name in model_names:
        if 'history' in results[model_name] and results[model_name]['history']:
            history = results[model_name]['history'].history
            final_train_loss = history['loss'][-1]
            final_val_loss = history['val_loss'][-1] if 'val_loss' in history else None
            min_train_loss = min(history['loss'])
            min_val_loss = min(history['val_loss']) if 'val_loss' in history else None
            
            loss_data = {
                'Model': model_name,
                'Final_Train_Loss': final_train_loss,
                'Final_Val_Loss': final_val_loss,
                'Min_Train_Loss': min_train_loss,
                'Min_Val_Loss': min_val_loss,
                'Overfitting_Gap': abs(final_train_loss - final_val_loss) if final_val_loss else None
            }
            loss_summary.append(loss_data)
    
    if loss_summary:
        loss_df = pd.DataFrame(loss_summary)
        display(loss_df.round(6))
        
        # Find best models
        best_train_model = loss_df.loc[loss_df['Final_Train_Loss'].idxmin(), 'Model']
        best_val_model = loss_df.loc[loss_df['Final_Val_Loss'].idxmin(), 'Model'] if 'Final_Val_Loss' in loss_df.columns else None
        least_overfitting = loss_df.loc[loss_df['Overfitting_Gap'].idxmin(), 'Model'] if 'Overfitting_Gap' in loss_df.columns else None
        
        print(f"\n🏆 LOSS ANALYSIS RESULTS:")
        print(f"   🎯 Best Training Loss: {best_train_model}")
        if best_val_model:
            print(f"   ✅ Best Validation Loss: {best_val_model}")
        if least_overfitting:
            print(f"   ⚖️  Least Overfitting: {least_overfitting}")

# Create the final loss chart
create_final_loss_chart(results)

print(f"\n💾 COMPREHENSIVE RESULTS SAVED:")
print(f"   📊 Main results: {results_filename}")
print(f"   📈 Summary CSV: {summary_filename}")
print(f"   📋 Data info: {data_info_filename}")
print(f"   🤖 Model files: resnet_model.keras, cnn1d_model.keras, lstm_model.keras, cnn_lstm_model.keras")
print(f"   📸 Plot files:")
print(f"      📊 Comparison plots:")
print(f"         - model_comparison_results.png")
print(f"         - training_history_comparison.png")
print(f"      🔍 Individual model confusion matrices:")
print(f"         - resnet_confusion_matrices.png")
print(f"         - cnn1d_confusion_matrices.png")
print(f"         - lstm_confusion_matrices.png")
print(f"         - cnn_lstm_confusion_matrices.png")
print(f"      📈 Individual training histories:")
print(f"         - resnet_individual_training_history.png")
print(f"         - (Additional individual histories if run separately)")
print(f"      📊 Final loss analysis:")
print(f"         - final_training_validation_loss_chart.png")
print(f"         - combined_loss_comparison_chart.png")
print(f"      🎯 t-SNE visualizations: (Generated during training)")
print("\n✅ All models have been trained and evaluated!")
print("✅ All results, models, and visualizations have been saved!")
print("✅ Use the analysis notebooks to load and explore the results")
print("\n📋 ANALYSIS NOTEBOOKS:")
print("   📓 load_and_analyze_results.ipynb - Main results analysis")
print("   📓 advanced_results_analysis.ipynb - Deep dive analysis")
print("\n🎨 ADDITIONAL PLOTS AVAILABLE IN ANALYSIS NOTEBOOKS:")
print("   📊 comprehensive_model_analysis.png")
print("   📈 per_class_f1_analysis.png")
print("   🔍 validation_confusion_matrices.png")
print("   🔍 training_confusion_matrices.png")
print("   📈 detailed_training_history.png")


