namO_Acc_11TH; 
% <PERSON><PERSON><PERSON> dữ liệu từ Matrix_Case
Label   = Matrix_Case(1, :); % Nhãn case, thường là 0,1,2,...
Element = Matrix_Case(2, :); % <PERSON><PERSON> hiệu phần tử bị hư hỏng
Damage  = Matrix_Case(3, :); % % hư hỏng

% <PERSON>ì<PERSON> dải nhãn và phần tử, dùng cho việc vẽ ma trận
label_list   = unique(Label);
element_list = unique(Element);

% Tạo lưới dữ liệu rỗng
Z = zeros(length(element_list), length(label_list));

% Gán giá trị vào đúng vị trí trên ma trận
for i = 1:length(Label)
    row = find(element_list == Element(i));
    col = find(label_list   == Label(i));
    Z(row, col) = Damage(i);
end

figure('Color','w');
h = bar3(Z);

% Chọn colormap hài hòa
colormap(parula);
colorbar;

% <PERSON><PERSON><PERSON> cho c<PERSON><PERSON> c<PERSON><PERSON> sáng r<PERSON> hơn (nế<PERSON> cần)
for k = 1:length(h)
    zdata = get(h(k),'ZData');
    set(h(k), 'CData', zdata, 'FaceColor','interp', 'EdgeColor','k', 'LineWidth', 0.8);
end

% Nhãn trục
xlabel('Label (Case)','FontSize',14,'FontWeight','bold')
ylabel('Element','FontSize',14,'FontWeight','bold')
zlabel('Damage level (%)','FontSize',14,'FontWeight','bold')

set(gca, 'XTick', 1:length(label_list), 'XTickLabel', label_list, ...
         'YTick', 1:length(element_list), 'YTickLabel', element_list, ...
         'FontSize',12, 'LineWidth',1.2)
zlim([0 max(Damage)+10]) % hoặc zlim([0 50]) nếu muốn cố định

view(-40, 30)
box on
grid on
title('Damage Matrix Case Visualization', 'FontSize',16,'FontWeight','bold')

% Thêm giá trị nổi bật trên đầu mỗi cột
for i = 1:size(Z,1)
    for j = 1:size(Z,2)
        if Z(i,j) > 0
            % Viền trắng để nổi chữ
            text(j, i, Z(i,j)+2, sprintf('%.1f', Z(i,j)), ...
                'HorizontalAlignment','center', ...
                'VerticalAlignment','bottom', ...
                'FontSize',13, ...
                'FontWeight','bold', ...
                'Color','k', ...
                'BackgroundColor','w', ...
                'Margin',1);
        end
    end
end