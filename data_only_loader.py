import os
import numpy as np
from load_data import (
    load_mat_data, 
    prepare_input_data, 
    augment_time_series_data,
    reshape_time_series_data_v8,
    prepare_train_test_data
)

def load_and_prepare_data():
    """Load and prepare the data using functions from load_data.py"""
    print("🔄 Loading and preparing data...")
    
    try:
        # Load raw data
        print("1. Loading .mat files...")
        all_data = load_mat_data('data')
        print(f"   ✅ Loaded {len(all_data)} data files")
        
        # Show what data files we have
        print("   Available data files:")
        for key in sorted(all_data.keys()):
            shape = all_data[key].shape
            print(f"     - {key}: {shape}")
        
        # Prepare input data
        print("2. Preparing input data...")
        input_data, output_labels = prepare_input_data(all_data)
        
        # Augment data
        print("3. Augmenting time series data...")
        augmented_data, augmented_labels = augment_time_series_data(
            input_data, output_labels, num_augmentations=4
        )
        
        # Reshape data
        print("4. Reshaping data...")
        reshaped_data, reshaped_labels = reshape_time_series_data_v8(
            augmented_data, augmented_labels, 
            segments_per_new_sample=10, 
            segment_length=4000
        )
        
        # Prepare train/test split
        print("5. Preparing train/test data...")
        X_train, X_valid, y_train, y_valid = prepare_train_test_data(
            reshaped_data, reshaped_labels, test_size=0.2, random_state=42
        )
        
        data_dict = {
            'raw_data': all_data,
            'input_data': input_data,
            'output_labels': output_labels,
            'augmented_data': augmented_data,
            'augmented_labels': augmented_labels,
            'reshaped_data': reshaped_data,
            'reshaped_labels': reshaped_labels,
            'X_train': X_train,
            'X_valid': X_valid,
            'y_train': y_train,
            'y_valid': y_valid
        }
        
        print("✅ Data preparation completed successfully!")
        return data_dict
        
    except Exception as e:
        print(f"❌ Error in data preparation: {str(e)}")
        import traceback
        traceback.print_exc()
        return None

def check_model_files():
    """Check what model files are available"""
    model_files = [
        '1d_cnn_model.keras',
        'cnn_lstm_model.keras', 
        'lstm_model.keras',
        'resnet_model.keras'
    ]
    
    print("🔍 Checking model files...")
    available_models = []
    
    for model_file in model_files:
        if os.path.exists(model_file):
            size = os.path.getsize(model_file)
            size_mb = size / (1024 * 1024)
            print(f"  ✅ {model_file}: {size_mb:.1f} MB")
            available_models.append(model_file)
        else:
            print(f"  ❌ {model_file}: Not found")
    
    return available_models

def check_results_file():
    """Check the results file without loading it"""
    results_file = 'model_comparison_results_20250628_001945.pkl'
    
    if os.path.exists(results_file):
        size = os.path.getsize(results_file)
        size_kb = size / 1024
        print(f"📊 Results file: {results_file} ({size_kb:.1f} KB)")
        print("   ⚠️  Contains custom classes - requires TensorFlow environment to load")
        return True
    else:
        print(f"❌ Results file not found: {results_file}")
        return False

def analyze_data_structure(data_dict):
    """Analyze and display the structure of loaded data"""
    if not data_dict:
        return
    
    print("\n📊 DATA ANALYSIS")
    print("="*50)
    
    # Raw data analysis
    print("Raw Data:")
    raw_data = data_dict['raw_data']
    for key in sorted(raw_data.keys()):
        shape = raw_data[key].shape
        print(f"  {key}: {shape}")
    
    # Processed data shapes
    print(f"\nProcessed Data Shapes:")
    print(f"  Input data: {data_dict['input_data'].shape}")
    print(f"  Output labels: {data_dict['output_labels'].shape}")
    print(f"  Augmented data: {data_dict['augmented_data'].shape}")
    print(f"  Reshaped data: {data_dict['reshaped_data'].shape}")
    
    # Training/validation split
    print(f"\nTrain/Validation Split:")
    print(f"  X_train: {data_dict['X_train'].shape}")
    print(f"  X_valid: {data_dict['X_valid'].shape}")
    print(f"  y_train: {data_dict['y_train'].shape}")
    print(f"  y_valid: {data_dict['y_valid'].shape}")
    
    # Label analysis
    y_train = data_dict['y_train']
    y_valid = data_dict['y_valid']
    
    print(f"\nLabel Analysis:")
    print(f"  Number of classes: {len(np.unique(y_train))}")
    print(f"  Label range: {np.min(y_train):.1f} to {np.max(y_train):.1f}")
    print(f"  Class distribution (train): {np.bincount(y_train.astype(int))}")
    
    # Data statistics
    X_train = data_dict['X_train']
    print(f"\nData Statistics (X_train):")
    print(f"  Mean: {np.mean(X_train):.6f}")
    print(f"  Std: {np.std(X_train):.6f}")
    print(f"  Min: {np.min(X_train):.6f}")
    print(f"  Max: {np.max(X_train):.6f}")

def create_model_loading_instructions():
    """Create instructions for loading models"""
    print("\n🔧 MODEL LOADING INSTRUCTIONS")
    print("="*50)
    print("Due to environment compatibility issues, load models manually:")
    print()
    print("1. For TensorFlow/Keras models:")
    print("   import tensorflow as tf")
    print("   model = tf.keras.models.load_model('lstm_model.keras')")
    print()
    print("2. Available model files:")
    model_files = [
        '1d_cnn_model.keras',
        'cnn_lstm_model.keras', 
        'lstm_model.keras',
        'resnet_model.keras'
    ]
    
    for model_file in model_files:
        if os.path.exists(model_file):
            print(f"   - {model_file}")
    
    print()
    print("3. Example usage:")
    print("   # Load a model")
    print("   import tensorflow as tf")
    print("   lstm_model = tf.keras.models.load_model('lstm_model.keras')")
    print("   ")
    print("   # Make predictions")
    print("   predictions = lstm_model.predict(data['X_valid'])")
    print("   ")
    print("   # Evaluate model")
    print("   loss, accuracy = lstm_model.evaluate(data['X_valid'], data['y_valid'])")

def main():
    """Main function to load data and provide model information"""
    print("🚀 Data Loader (Models require separate TensorFlow loading)")
    print("="*60)
    
    # Check model files
    available_models = check_model_files()
    
    # Check results file
    results_available = check_results_file()
    
    # Load and prepare data
    data_dict = load_and_prepare_data()
    
    # Analyze data structure
    analyze_data_structure(data_dict)
    
    # Provide model loading instructions
    create_model_loading_instructions()
    
    print("\n" + "="*60)
    print("📋 SUMMARY")
    print("="*60)
    print(f"✅ Available model files: {len(available_models)}")
    print(f"✅ Results file available: {'Yes' if results_available else 'No'}")
    print(f"✅ Data loaded and processed: {'Yes' if data_dict else 'No'}")
    
    if data_dict:
        print(f"✅ Ready for model training/evaluation")
        print(f"   - Training samples: {data_dict['X_train'].shape[0]}")
        print(f"   - Validation samples: {data_dict['X_valid'].shape[0]}")
        print(f"   - Input shape: {data_dict['X_train'].shape[1:]}")
        print(f"   - Number of classes: {len(np.unique(data_dict['y_train']))}")
    
    return {
        'available_models': available_models,
        'results_available': results_available,
        'data': data_dict
    }

if __name__ == "__main__":
    # Load everything
    loaded_data = main()
    
    # Make variables available in global scope for interactive use
    available_models = loaded_data['available_models']
    results_available = loaded_data['results_available']
    data = loaded_data['data']
    
    print("\n" + "="*60)
    print("✅ LOADING COMPLETE!")
    print("="*60)
    print("Available variables:")
    print("  - available_models: List of available .keras model files")
    print("  - results_available: Boolean indicating if results file exists")
    print("  - data: Dictionary with all processed data")
    print("\nData dictionary keys:")
    if data:
        for key in data.keys():
            print(f"  - data['{key}']")
