"""
🔍 TEST FOLDER STRUCTURE
This script tests the folder structure creation for t-SNE export
without requiring TensorFlow
"""

import os
import datetime

def test_folder_creation():
    """Test creating the folder structure"""
    print("🔍 Testing t-SNE folder structure creation...")
    
    # Create main output folder with timestamp
    timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
    main_output_dir = f"tsne_results_{timestamp}"
    
    try:
        os.makedirs(main_output_dir, exist_ok=True)
        
        # Create subfolders for organization
        subfolders = {
            "visualizations": "Individual epoch visualizations",
            "coordinates": "t-SNE coordinate data",
            "summaries": "Per-model summaries",
            "model_comparison": "Cross-model comparisons"
        }
        
        created_folders = []
        for folder_name, description in subfolders.items():
            folder_path = os.path.join(main_output_dir, folder_name)
            os.makedirs(folder_path, exist_ok=True)
            created_folders.append((folder_name, description))
            print(f"   ✅ Created: {folder_name}/ - {description}")
        
        # Create sample files to test structure
        sample_files = [
            ("visualizations/ResNet_epoch_020.png", "Sample visualization"),
            ("coordinates/ResNet_epoch_020_coords.npz", "Sample coordinates"),
            ("summaries/ResNet_summary.png", "Sample summary"),
            ("model_comparison/epoch_020_comparison.png", "Sample comparison"),
            ("complete_overview.png", "Overall summary"),
            ("README.md", "Index file")
        ]
        
        for file_path, description in sample_files:
            full_path = os.path.join(main_output_dir, file_path)
            with open(full_path, 'w') as f:
                f.write(f"# {description}\nGenerated: {datetime.datetime.now()}\n")
            print(f"   📄 Created: {file_path} - {description}")
        
        print(f"\n✅ Folder structure test completed!")
        print(f"📁 Main folder: {main_output_dir}")
        
        # Display structure
        print(f"\n📁 Folder structure:")
        print(f"   {main_output_dir}/")
        for folder_name, _ in created_folders:
            files_in_folder = [f for f in sample_files if f[0].startswith(folder_name)]
            print(f"   ├── {folder_name}/ ({len(files_in_folder)} files)")
        
        root_files = [f for f in sample_files if '/' not in f[0]]
        for file_path, _ in root_files:
            print(f"   ├── {file_path}")
        
        print(f"\n🎯 Expected for real run:")
        print(f"   📊 Individual visualizations: 20 files (4 models × 5 epochs)")
        print(f"   📈 Coordinate files: 20 files")
        print(f"   📋 Model summaries: 4 files")
        print(f"   🔄 Model comparisons: 5 files")
        print(f"   🎯 Overall summary: 1 file")
        print(f"   📄 Index file: 1 file")
        print(f"   📊 Total: 51 files")
        
        return True, main_output_dir
        
    except Exception as e:
        print(f"   ❌ Error creating folder structure: {e}")
        return False, None

def create_sample_readme(main_output_dir):
    """Create a sample README file"""
    print(f"\n📋 Creating sample README...")
    
    readme_content = f"""# t-SNE Visualization Results
Generated on: {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

## Summary
- **Models processed**: 4/4 (ResNet, 1D_CNN, LSTM, CNN_LSTM)
- **Epochs visualized**: [20, 40, 60, 80, 100]
- **Validation samples**: 247
- **Classes**: 11

## Folder Structure

### 📊 Individual Visualizations (`visualizations/`)
- `ResNet_epoch_020.png` - ResNet at epoch 20
- `ResNet_epoch_040.png` - ResNet at epoch 40
- `ResNet_epoch_060.png` - ResNet at epoch 60
- `ResNet_epoch_080.png` - ResNet at epoch 80
- `ResNet_epoch_100.png` - ResNet at epoch 100
- `1D_CNN_epoch_020.png` - 1D CNN at epoch 20
- `1D_CNN_epoch_040.png` - 1D CNN at epoch 40
- `1D_CNN_epoch_060.png` - 1D CNN at epoch 60
- `1D_CNN_epoch_080.png` - 1D CNN at epoch 80
- `1D_CNN_epoch_100.png` - 1D CNN at epoch 100
- `LSTM_epoch_020.png` - LSTM at epoch 20
- `LSTM_epoch_040.png` - LSTM at epoch 40
- `LSTM_epoch_060.png` - LSTM at epoch 60
- `LSTM_epoch_080.png` - LSTM at epoch 80
- `LSTM_epoch_100.png` - LSTM at epoch 100
- `CNN_LSTM_epoch_020.png` - CNN-LSTM at epoch 20
- `CNN_LSTM_epoch_040.png` - CNN-LSTM at epoch 40
- `CNN_LSTM_epoch_060.png` - CNN-LSTM at epoch 60
- `CNN_LSTM_epoch_080.png` - CNN-LSTM at epoch 80
- `CNN_LSTM_epoch_100.png` - CNN-LSTM at epoch 100

### 📈 Coordinate Data (`coordinates/`)
- `ResNet_epoch_020_coords.npz` - ResNet epoch 20 coordinates
- `ResNet_epoch_040_coords.npz` - ResNet epoch 40 coordinates
- `ResNet_epoch_060_coords.npz` - ResNet epoch 60 coordinates
- `ResNet_epoch_080_coords.npz` - ResNet epoch 80 coordinates
- `ResNet_epoch_100_coords.npz` - ResNet epoch 100 coordinates
- `1D_CNN_epoch_020_coords.npz` - 1D CNN epoch 20 coordinates
- `1D_CNN_epoch_040_coords.npz` - 1D CNN epoch 40 coordinates
- `1D_CNN_epoch_060_coords.npz` - 1D CNN epoch 60 coordinates
- `1D_CNN_epoch_080_coords.npz` - 1D CNN epoch 80 coordinates
- `1D_CNN_epoch_100_coords.npz` - 1D CNN epoch 100 coordinates
- `LSTM_epoch_020_coords.npz` - LSTM epoch 20 coordinates
- `LSTM_epoch_040_coords.npz` - LSTM epoch 40 coordinates
- `LSTM_epoch_060_coords.npz` - LSTM epoch 60 coordinates
- `LSTM_epoch_080_coords.npz` - LSTM epoch 80 coordinates
- `LSTM_epoch_100_coords.npz` - LSTM epoch 100 coordinates
- `CNN_LSTM_epoch_020_coords.npz` - CNN-LSTM epoch 20 coordinates
- `CNN_LSTM_epoch_040_coords.npz` - CNN-LSTM epoch 40 coordinates
- `CNN_LSTM_epoch_060_coords.npz` - CNN-LSTM epoch 60 coordinates
- `CNN_LSTM_epoch_080_coords.npz` - CNN-LSTM epoch 80 coordinates
- `CNN_LSTM_epoch_100_coords.npz` - CNN-LSTM epoch 100 coordinates

### 📋 Model Summaries (`summaries/`)
- `ResNet_summary.png` - ResNet evolution across all epochs
- `1D_CNN_summary.png` - 1D CNN evolution across all epochs
- `LSTM_summary.png` - LSTM evolution across all epochs
- `CNN_LSTM_summary.png` - CNN-LSTM evolution across all epochs

### 🔄 Model Comparisons (`model_comparison/`)
- `epoch_020_comparison.png` - All models at epoch 20
- `epoch_040_comparison.png` - All models at epoch 40
- `epoch_060_comparison.png` - All models at epoch 60
- `epoch_080_comparison.png` - All models at epoch 80
- `epoch_100_comparison.png` - All models at epoch 100

### 🎯 Overall Summary
- `complete_overview.png` - All models and epochs in one view

## Usage
1. **Individual visualizations**: View specific model-epoch combinations
2. **Coordinate data**: Load NPZ files for further analysis
   ```python
   import numpy as np
   data = np.load('coordinates/ResNet_epoch_020_coords.npz')
   coords = data['tsne_coordinates']
   labels = data['labels']
   ```
3. **Model summaries**: See epoch progression for each model
4. **Model comparisons**: Compare different models at same epochs
5. **Complete overview**: See everything at once

## Analysis Notes
- t-SNE parameters: perplexity=30, n_iter=1000
- PCA preprocessing applied when features > 50 dimensions
- Consistent color scheme across all visualizations
- High-resolution exports (300 DPI)
- Validation set used for t-SNE (247 samples)

## File Naming Convention
- Individual: `[Model]_epoch_[XXX].png`
- Coordinates: `[Model]_epoch_[XXX]_coords.npz`
- Summaries: `[Model]_summary.png`
- Comparisons: `epoch_[XXX]_comparison.png`

## Total Files: 51
- 20 individual visualizations
- 20 coordinate files
- 4 model summaries
- 5 model comparisons
- 1 complete overview
- 1 README file
"""
    
    readme_path = os.path.join(main_output_dir, "README.md")
    with open(readme_path, 'w') as f:
        f.write(readme_content)
    
    print(f"   ✅ Sample README created with complete file listing")

def main():
    """Main test function"""
    print("🔍 TESTING t-SNE SINGLE FOLDER STRUCTURE")
    print("="*50)
    
    success, folder_path = test_folder_creation()
    
    if success:
        create_sample_readme(folder_path)
        
        print(f"\n✅ TEST SUCCESSFUL!")
        print(f"📁 Test folder created: {folder_path}")
        print(f"🎯 This demonstrates the exact structure that will be created")
        print(f"📋 Check the README.md for complete file listing")
        
        print(f"\n🚀 To run the actual t-SNE export:")
        print(f"   1. Copy EXPORT_TSNE_SINGLE_FOLDER.py to TensorFlow environment")
        print(f"   2. Copy your data and model files")
        print(f"   3. Run: python EXPORT_TSNE_SINGLE_FOLDER.py")
        print(f"   4. Get 51 files in organized folder structure!")
        
    else:
        print(f"\n❌ TEST FAILED!")
        print(f"   Check folder permissions and try again")

if __name__ == "__main__":
    main()
