import os
import numpy as np
import tensorflow as tf
from tensorflow import keras
import matplotlib.pyplot as plt
from load_data import (
    load_mat_data, 
    prepare_input_data, 
    augment_time_series_data,
    reshape_time_series_data_v8,
    prepare_train_test_data
)

def load_keras_models():
    """Load all 4 Keras models with multiple loading strategies"""
    models = {}
    model_files = [
        '1d_cnn_model.keras',
        'cnn_lstm_model.keras',
        'lstm_model.keras',
        'resnet_model.keras'
    ]

    print("Loading Keras models...")
    for model_file in model_files:
        if os.path.exists(model_file):
            model_name = model_file.replace('.keras', '')
            print(f"Attempting to load: {model_name}")

            # Try different loading methods
            loaded = False

            # Method 1: Standard keras load_model
            try:
                models[model_name] = keras.models.load_model(model_file)
                print(f"✅ Successfully loaded: {model_name} (standard method)")
                print(f"   Model summary: {models[model_name].count_params()} parameters")
                loaded = True
            except Exception as e:
                print(f"   ⚠️  Standard loading failed: {str(e)}")

            # Method 2: Try with compile=False
            if not loaded:
                try:
                    models[model_name] = keras.models.load_model(model_file, compile=False)
                    print(f"✅ Successfully loaded: {model_name} (compile=False)")
                    print(f"   Model summary: {models[model_name].count_params()} parameters")
                    loaded = True
                except Exception as e:
                    print(f"   ⚠️  Loading with compile=False failed: {str(e)}")

            # Method 3: Try with custom_objects
            if not loaded:
                try:
                    models[model_name] = keras.models.load_model(
                        model_file,
                        custom_objects=None,
                        compile=False
                    )
                    print(f"✅ Successfully loaded: {model_name} (custom_objects=None)")
                    print(f"   Model summary: {models[model_name].count_params()} parameters")
                    loaded = True
                except Exception as e:
                    print(f"   ❌ All loading methods failed for {model_file}: {str(e)}")
        else:
            print(f"⚠️  Model file not found: {model_file}")

    return models

def load_results():
    """Load the model comparison results from pickle file with error handling"""
    results_file = 'model_comparison_results_20250628_001945.pkl'

    if os.path.exists(results_file):
        print(f"Attempting to load results from: {results_file}")

        # Try different loading strategies
        try:
            with open(results_file, 'rb') as f:
                results = pickle.load(f)
            print(f"✅ Successfully loaded results from: {results_file}")

            # Display results structure
            if isinstance(results, dict):
                print("Results structure:")
                for key, value in results.items():
                    if isinstance(value, (list, np.ndarray)):
                        print(f"  {key}: {type(value)} with length {len(value)}")
                    else:
                        print(f"  {key}: {type(value)} - {value}")

            return results

        except (AttributeError, ModuleNotFoundError) as e:
            print(f"⚠️  Pickle loading failed due to missing class/module: {str(e)}")
            print("   Attempting to load with ignore_errors...")

            # Try to load with a custom unpickler that ignores missing classes
            try:
                import pickle

                class SafeUnpickler(pickle.Unpickler):
                    def find_class(self, module, name):
                        # Return a dummy class for missing classes
                        if module == '__main__':
                            return type(name, (), {})
                        return super().find_class(module, name)

                with open(results_file, 'rb') as f:
                    results = SafeUnpickler(f).load()

                print(f"✅ Successfully loaded results with safe unpickler")
                print("⚠️  Note: Some custom objects may be replaced with dummy objects")

                # Display what we could load
                if isinstance(results, dict):
                    print("Available keys:")
                    for key in results.keys():
                        print(f"  - {key}")

                return results

            except Exception as e2:
                print(f"❌ Safe unpickler also failed: {str(e2)}")
                return None

        except Exception as e:
            print(f"❌ Error loading results: {str(e)}")
            return None
    else:
        print(f"⚠️  Results file not found: {results_file}")
        return None

def load_and_prepare_data():
    """Load and prepare the data using functions from load_data.py"""
    print("\nLoading and preparing data...")
    
    try:
        # Load raw data
        print("1. Loading .mat files...")
        all_data = load_mat_data('data')
        print(f"   Loaded {len(all_data)} data files")
        
        # Prepare input data
        print("2. Preparing input data...")
        input_data, output_labels = prepare_input_data(all_data)
        
        # Augment data
        print("3. Augmenting time series data...")
        augmented_data, augmented_labels = augment_time_series_data(
            input_data, output_labels, num_augmentations=4
        )
        
        # Reshape data
        print("4. Reshaping data...")
        reshaped_data, reshaped_labels = reshape_time_series_data_v8(
            augmented_data, augmented_labels, 
            segments_per_new_sample=10, 
            segment_length=4000
        )
        
        # Prepare train/test split
        print("5. Preparing train/test data...")
        X_train, X_valid, y_train, y_valid = prepare_train_test_data(
            reshaped_data, reshaped_labels, test_size=0.2, random_state=42
        )
        
        data_dict = {
            'raw_data': all_data,
            'input_data': input_data,
            'output_labels': output_labels,
            'augmented_data': augmented_data,
            'augmented_labels': augmented_labels,
            'reshaped_data': reshaped_data,
            'reshaped_labels': reshaped_labels,
            'X_train': X_train,
            'X_valid': X_valid,
            'y_train': y_train,
            'y_valid': y_valid
        }
        
        print("✅ Data preparation completed successfully!")
        return data_dict
        
    except Exception as e:
        print(f"❌ Error in data preparation: {str(e)}")
        return None

def display_model_info(models):
    """Display information about loaded models"""
    print("\n" + "="*60)
    print("MODEL INFORMATION")
    print("="*60)
    
    for model_name, model in models.items():
        print(f"\n📊 {model_name.upper()}")
        print("-" * 40)
        print(f"Input shape: {model.input_shape}")
        print(f"Output shape: {model.output_shape}")
        print(f"Total parameters: {model.count_params():,}")
        print(f"Trainable parameters: {sum([tf.keras.backend.count_params(w) for w in model.trainable_weights]):,}")
        
        # Display layer summary
        print("Layers:")
        for i, layer in enumerate(model.layers[:5]):  # Show first 5 layers
            print(f"  {i+1}. {layer.name} ({layer.__class__.__name__})")
        if len(model.layers) > 5:
            print(f"  ... and {len(model.layers) - 5} more layers")

def evaluate_models_on_data(models, data_dict):
    """Evaluate all models on the validation data"""
    if not models or not data_dict:
        print("⚠️  Cannot evaluate models - missing models or data")
        return None
    
    print("\n" + "="*60)
    print("MODEL EVALUATION")
    print("="*60)
    
    X_valid = data_dict['X_valid']
    y_valid = data_dict['y_valid']
    
    evaluation_results = {}
    
    for model_name, model in models.items():
        try:
            print(f"\n🔍 Evaluating {model_name}...")
            
            # Make predictions
            predictions = model.predict(X_valid, verbose=0)
            
            # Calculate metrics
            if model.output_shape[-1] == 1:  # Regression
                mse = np.mean((predictions.flatten() - y_valid) ** 2)
                mae = np.mean(np.abs(predictions.flatten() - y_valid))
                print(f"   MSE: {mse:.4f}")
                print(f"   MAE: {mae:.4f}")
                evaluation_results[model_name] = {
                    'predictions': predictions,
                    'mse': mse,
                    'mae': mae,
                    'type': 'regression'
                }
            else:  # Classification
                predicted_classes = np.argmax(predictions, axis=1)
                accuracy = np.mean(predicted_classes == y_valid)
                print(f"   Accuracy: {accuracy:.4f}")
                evaluation_results[model_name] = {
                    'predictions': predictions,
                    'predicted_classes': predicted_classes,
                    'accuracy': accuracy,
                    'type': 'classification'
                }
                
        except Exception as e:
            print(f"❌ Error evaluating {model_name}: {str(e)}")
            evaluation_results[model_name] = {'error': str(e)}
    
    return evaluation_results

def main():
    """Main function to load everything"""
    print("🚀 Loading Models, Results, and Data")
    print("="*60)
    
    # Load models
    models = load_keras_models()
    
    # Load results
    results = load_results()
    
    # Load and prepare data
    data_dict = load_and_prepare_data()
    
    # Display model information
    if models:
        display_model_info(models)
    
    # Evaluate models on data
    evaluation_results = None
    if models and data_dict:
        evaluation_results = evaluate_models_on_data(models, data_dict)
    
    # Return everything
    return {
        'models': models,
        'results': results,
        'data': data_dict,
        'evaluation': evaluation_results
    }

if __name__ == "__main__":
    # Load everything
    loaded_data = main()
    
    # Make variables available in global scope for interactive use
    models = loaded_data['models']
    results = loaded_data['results']
    data = loaded_data['data']
    evaluation = loaded_data['evaluation']
    
    print("\n" + "="*60)
    print("✅ LOADING COMPLETE!")
    print("="*60)
    print("Available variables:")
    print("  - models: Dictionary of loaded Keras models")
    print("  - results: Loaded pickle results")
    print("  - data: Dictionary with all data (raw, processed, train/test)")
    print("  - evaluation: Model evaluation results")
    print("\nExample usage:")
    print("  models['lstm_model'].summary()")
    print("  data['X_train'].shape")
    print("  results.keys() if results else 'No results loaded'")
