"""
COMPLETE PREDICTION CODE FOR ALL 4 MODELS
Copy this entire file to a TensorFlow-compatible environment and run it
"""
import tensorflow as tf
import numpy as np
import pandas as pd
from datetime import datetime

print("🔮 MAKING PREDICTIONS WITH ALL 4 MODELS")
print("="*60)

# STEP 1: Load the data
print("📊 Loading data...")

# Try to load from exported files first
try:
    data = np.load('exported_results_20250728_085759/data/complete_dataset.npz')
    X_train = data['X_train']
    X_valid = data['X_valid'] 
    y_train = data['y_train']
    y_valid = data['y_valid']
    print("   SUCCESS: Loaded from exported NPZ file")
except:
    try:
        # Load individual files
        X_train = np.load('exported_results_20250728_085759/data/X_train.npy')
        X_valid = np.load('exported_results_20250728_085759/data/X_valid.npy')
        y_train = np.load('exported_results_20250728_085759/data/y_train.npy')
        y_valid = np.load('exported_results_20250728_085759/data/y_valid.npy')
        print("   SUCCESS: Loaded from individual NPY files")
    except:
        print("   ERROR: Could not load data files")
        print("   Make sure you're in the correct directory with the exported data")
        exit()

print(f"Data shapes:")
print(f"  X_train: {X_train.shape}")
print(f"  X_valid: {X_valid.shape}")
print(f"  y_train: {y_train.shape}")
print(f"  y_valid: {y_valid.shape}")
print(f"  Classes: {len(np.unique(y_train))} (range: {np.min(y_train)} to {np.max(y_train)})")

# STEP 2: Load all 4 models
print("\n🤖 Loading models...")
models = {}

model_files = {
    '1d_cnn_model': '1d_cnn_model.keras',
    'cnn_lstm_model': 'cnn_lstm_model.keras',
    'lstm_model': 'lstm_model.keras',
    'resnet_model': 'resnet_model.keras'
}

for name, file_path in model_files.items():
    try:
        models[name] = tf.keras.models.load_model(file_path)
        print(f"   SUCCESS {name}: {models[name].count_params():,} parameters")
        print(f"      Input: {models[name].input_shape}")
        print(f"      Output: {models[name].output_shape}")
    except Exception as e:
        print(f"   FAILED {name}: {e}")

print(f"\nSuccessfully loaded {len(models)}/4 models")

if len(models) == 0:
    print("ERROR: No models could be loaded!")
    exit()

# STEP 3: Make predictions with all models
print("\n🔮 Making predictions...")
predictions_results = {}

for model_name, model in models.items():
    print(f"\nPredicting with {model_name}...")
    
    try:
        # Make predictions on validation data
        raw_predictions = model.predict(X_valid, verbose=0)
        
        # Convert to class predictions
        if len(raw_predictions.shape) > 1 and raw_predictions.shape[1] > 1:
            # Multi-class classification
            predicted_classes = np.argmax(raw_predictions, axis=1)
            prediction_probabilities = raw_predictions
        else:
            # Single output - treat as regression then convert to classes
            if len(raw_predictions.shape) > 1:
                raw_predictions = raw_predictions.flatten()
            predicted_classes = np.round(raw_predictions).astype(int)
            # Ensure predictions are in valid range
            predicted_classes = np.clip(predicted_classes, 0, len(np.unique(y_train))-1)
            prediction_probabilities = raw_predictions
        
        # Calculate accuracy
        accuracy = np.mean(predicted_classes == y_valid)
        
        # Store results
        predictions_results[model_name] = {
            'predicted_classes': predicted_classes,
            'raw_predictions': raw_predictions,
            'probabilities': prediction_probabilities,
            'accuracy': accuracy
        }
        
        print(f"   SUCCESS {model_name}:")
        print(f"      Accuracy: {accuracy:.4f} ({accuracy*100:.2f}%)")
        print(f"      Predictions shape: {raw_predictions.shape}")
        print(f"      Sample predictions: {predicted_classes[:10]}")
        print(f"      Actual labels:      {y_valid[:10]}")
        
    except Exception as e:
        print(f"   ERROR with {model_name}: {e}")

# STEP 4: Compare all model performances
print("\n📊 MODEL COMPARISON")
print("="*60)

# Create comparison DataFrame
comparison_data = {
    'sample_id': range(len(y_valid)),
    'actual': y_valid
}

for model_name, results in predictions_results.items():
    if 'predicted_classes' in results:
        comparison_data[f'{model_name}_pred'] = results['predicted_classes']

comparison_df = pd.DataFrame(comparison_data)

# Print accuracy summary
print("🎯 ACCURACY SUMMARY:")
accuracies = []
for model_name, results in predictions_results.items():
    if 'accuracy' in results:
        acc = results['accuracy']
        accuracies.append((model_name, acc))
        print(f"   {model_name:15s}: {acc:.4f} ({acc*100:.2f}%)")

# Find best model
if accuracies:
    best_model = max(accuracies, key=lambda x: x[1])
    print(f"\n🏆 BEST MODEL: {best_model[0]} with {best_model[1]:.4f} accuracy")

# STEP 5: Detailed analysis
print("\n📈 DETAILED ANALYSIS:")

# Class distribution
print("\nClass distribution:")
actual_dist = np.bincount(y_valid)
print(f"Actual:     {actual_dist}")

for model_name, results in predictions_results.items():
    if 'predicted_classes' in results:
        pred_dist = np.bincount(results['predicted_classes'], minlength=len(actual_dist))
        print(f"{model_name:12s}: {pred_dist}")

# Per-class accuracy
print("\nPer-class accuracy:")
for model_name, results in predictions_results.items():
    if 'predicted_classes' in results:
        print(f"\n{model_name}:")
        y_pred = results['predicted_classes']
        for class_id in np.unique(y_valid):
            mask = y_valid == class_id
            if np.sum(mask) > 0:
                class_acc = np.mean(y_pred[mask] == class_id)
                count = np.sum(mask)
                print(f"  Class {class_id}: {class_acc:.4f} ({count} samples)")

# Confusion matrix summary
print("\nConfusion summary (first 20 samples):")
print("Sample | Actual | " + " | ".join([f"{name:8s}" for name in predictions_results.keys()]))
print("-" * (20 + len(predictions_results) * 11))

for i in range(min(20, len(y_valid))):
    row = f"{i:6d} | {y_valid[i]:6d} | "
    for model_name, results in predictions_results.items():
        if 'predicted_classes' in results:
            pred = results['predicted_classes'][i]
            correct = "✓" if pred == y_valid[i] else "✗"
            row += f"{pred:6d}{correct:2s} | "
    print(row)

# STEP 6: Save results
print("\n💾 Saving results...")

# Save predictions to CSV
timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
csv_file = f"model_predictions_{timestamp}.csv"
comparison_df.to_csv(csv_file, index=False)
print(f"SUCCESS: Predictions saved to {csv_file}")

# Save detailed results
detailed_file = f"detailed_predictions_{timestamp}.csv"
detailed_data = {'sample_id': range(len(y_valid)), 'actual': y_valid}

for model_name, results in predictions_results.items():
    if 'predicted_classes' in results:
        detailed_data[f'{model_name}_pred'] = results['predicted_classes']
        detailed_data[f'{model_name}_correct'] = (results['predicted_classes'] == y_valid).astype(int)
        
        # Add probabilities if available
        if 'probabilities' in results and len(results['probabilities'].shape) > 1:
            probs = results['probabilities']
            if probs.shape[1] > 1:  # Multi-class probabilities
                for class_id in range(probs.shape[1]):
                    detailed_data[f'{model_name}_prob_class_{class_id}'] = probs[:, class_id]

detailed_df = pd.DataFrame(detailed_data)
detailed_df.to_csv(detailed_file, index=False)
print(f"SUCCESS: Detailed results saved to {detailed_file}")

# Save summary report
report_file = f"prediction_summary_{timestamp}.txt"
with open(report_file, 'w') as f:
    f.write("MODEL PREDICTION SUMMARY\n")
    f.write("="*50 + "\n")
    f.write(f"Generated: {datetime.now()}\n\n")
    
    f.write("DATASET INFO:\n")
    f.write(f"Validation samples: {len(y_valid)}\n")
    f.write(f"Number of classes: {len(np.unique(y_valid))}\n")
    f.write(f"Input shape: {X_valid.shape}\n\n")
    
    f.write("MODEL ACCURACIES:\n")
    for model_name, acc in accuracies:
        f.write(f"{model_name}: {acc:.4f} ({acc*100:.2f}%)\n")
    
    if accuracies:
        f.write(f"\nBEST MODEL: {best_model[0]} ({best_model[1]:.4f})\n")
    
    f.write(f"\nCLASS DISTRIBUTION:\n")
    f.write(f"Actual: {actual_dist}\n")
    for model_name, results in predictions_results.items():
        if 'predicted_classes' in results:
            pred_dist = np.bincount(results['predicted_classes'], minlength=len(actual_dist))
            f.write(f"{model_name}: {pred_dist}\n")

print(f"SUCCESS: Summary saved to {report_file}")

print("\n🎉 PREDICTION COMPLETE!")
print("="*60)
print(f"📊 Processed {len(y_valid)} validation samples")
print(f"🤖 Used {len(models)} models")
print(f"📄 Files created:")
print(f"   - {csv_file}")
print(f"   - {detailed_file}")
print(f"   - {report_file}")

if accuracies:
    print(f"\n🏆 FINAL RESULTS:")
    for model_name, acc in sorted(accuracies, key=lambda x: x[1], reverse=True):
        print(f"   {model_name:15s}: {acc:.4f} ({acc*100:.2f}%)")

print(f"\nAvailable variables:")
print(f"   - models: Dictionary of loaded models")
print(f"   - predictions_results: All prediction results")
print(f"   - comparison_df: DataFrame with all predictions")
print(f"   - X_valid, y_valid: Validation data and labels")
