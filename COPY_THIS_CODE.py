"""
🚀 COPY AND PASTE THIS CODE TO LOAD EVERYTHING
"""

# STEP 1: Load Models
import tensorflow as tf
import numpy as np

# Load all 4 models
models = {}
models['1d_cnn_model'] = tf.keras.models.load_model('1d_cnn_model.keras')
models['cnn_lstm_model'] = tf.keras.models.load_model('cnn_lstm_model.keras')
models['lstm_model'] = tf.keras.models.load_model('lstm_model.keras')
models['resnet_model'] = tf.keras.models.load_model('resnet_model.keras')

# Verify models loaded
for name, model in models.items():
    print(f'{name}: {model.count_params():,} parameters')

# STEP 2: Load Data (run the data loader first)
from FINAL_LOADER import load_everything
data, available_models, results_info = load_everything()

# Extract data
X_train = data['X_train']  # Training data (985, 10, 4000)
X_valid = data['X_valid']  # Validation data (247, 10, 4000)
y_train = data['y_train']  # Training labels (985,)
y_valid = data['y_valid']  # Validation labels (247,)

print(f"Data loaded: {X_train.shape[0]} training, {X_valid.shape[0]} validation samples")

# STEP 3: Evaluate All Models
print("\n🔍 Evaluating all models...")
for name, model in models.items():
    loss, accuracy = model.evaluate(X_valid, y_valid, verbose=0)
    print(f'{name}: Loss={loss:.4f}, Accuracy={accuracy:.4f}')

# STEP 4: Make Predictions
print("\n🔮 Making predictions...")
predictions = models['1d_cnn_model'].predict(X_valid)
predicted_classes = np.argmax(predictions, axis=1)
print(f'Predicted classes (first 10): {predicted_classes[:10]}')
print(f'Actual classes (first 10): {y_valid[:10]}')

# STEP 5: Load Results (optional)
import pickle
try:
    with open('model_comparison_results_20250628_001945.pkl', 'rb') as f:
        results = pickle.load(f)
    print('✅ Results loaded successfully!')
    print(f'Results keys: {list(results.keys()) if isinstance(results, dict) else type(results)}')
except Exception as e:
    print(f'⚠️ Error loading results: {e}')

print("\n🎉 Everything loaded successfully!")
print("Available variables:")
print("- models: Dictionary with 4 Keras models")
print("- data: Complete dataset dictionary") 
print("- X_train, X_valid, y_train, y_valid: Ready-to-use data")
print("- results: Model comparison results (if loaded)")
