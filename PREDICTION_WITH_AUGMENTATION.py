"""
🔮 COMPLETE PREDICTION WITH PROPER AUGMENTATION
This script uses the properly augmented dataset for predictions with all 4 models
"""
import tensorflow as tf
import numpy as np
import pandas as pd
from datetime import datetime

print("🔮 MAKING PREDICTIONS WITH ALL 4 MODELS (AUGMENTED DATA)")
print("="*70)

# STEP 1: Load the properly augmented data
print("📊 Loading augmented data...")

try:
    # Import the fixed data loader
    from fixed_data_loader import load_complete_dataset_with_augmentation
    
    print("   Loading complete dataset with augmentation...")
    data_dict = load_complete_dataset_with_augmentation()
    
    if data_dict:
        X_train = data_dict['X_train']
        X_valid = data_dict['X_valid'] 
        y_train = data_dict['y_train']
        y_valid = data_dict['y_valid']
        
        print("   ✅ SUCCESS: Loaded augmented dataset")
        print(f"   Training: {X_train.shape} (from {data_dict['input_data'].shape[0]} original samples)")
        print(f"   Validation: {X_valid.shape}")
        print(f"   Augmentation factor: {data_dict['augmented_data'].shape[0] / data_dict['input_data'].shape[0]:.1f}x")
    else:
        raise Exception("Failed to load augmented data")
        
except Exception as e:
    print(f"   ❌ Error loading augmented data: {e}")
    print("   Falling back to exported data...")
    
    try:
        # Fallback to exported data
        data = np.load('exported_results_20250728_085759/data/complete_dataset.npz')
        X_train = data['X_train']
        X_valid = data['X_valid'] 
        y_train = data['y_train']
        y_valid = data['y_valid']
        print("   ⚠️  Using exported data (may not include full augmentation)")
    except:
        print("   ❌ Could not load any data!")
        exit()

print(f"\nFinal data shapes:")
print(f"  X_train: {X_train.shape}")
print(f"  X_valid: {X_valid.shape}")
print(f"  y_train: {y_train.shape}")
print(f"  y_valid: {y_valid.shape}")
print(f"  Classes: {len(np.unique(y_train))} (range: {np.min(y_train)} to {np.max(y_train)})")

# STEP 2: Load all 4 models
print("\n🤖 Loading models...")
models = {}

model_files = {
    '1d_cnn_model': '1d_cnn_model.keras',
    'cnn_lstm_model': 'cnn_lstm_model.keras',
    'lstm_model': 'lstm_model.keras',
    'resnet_model': 'resnet_model.keras'
}

for name, file_path in model_files.items():
    try:
        models[name] = tf.keras.models.load_model(file_path)
        print(f"   ✅ {name}: {models[name].count_params():,} parameters")
        print(f"      Input: {models[name].input_shape}")
        print(f"      Output: {models[name].output_shape}")
    except Exception as e:
        print(f"   ❌ {name}: {e}")

print(f"\n📊 Successfully loaded {len(models)}/4 models")

if len(models) == 0:
    print("❌ ERROR: No models could be loaded!")
    exit()

# STEP 3: Make predictions with all models
print("\n🔮 Making predictions on augmented validation data...")
predictions_results = {}

for model_name, model in models.items():
    print(f"\n   Predicting with {model_name}...")
    
    try:
        # Make predictions on validation data
        raw_predictions = model.predict(X_valid, verbose=0)
        
        # Convert to class predictions
        if len(raw_predictions.shape) > 1 and raw_predictions.shape[1] > 1:
            # Multi-class classification
            predicted_classes = np.argmax(raw_predictions, axis=1)
            prediction_probabilities = raw_predictions
            max_probs = np.max(raw_predictions, axis=1)
        else:
            # Single output - treat as regression then convert to classes
            if len(raw_predictions.shape) > 1:
                raw_predictions = raw_predictions.flatten()
            predicted_classes = np.round(raw_predictions).astype(int)
            # Ensure predictions are in valid range
            predicted_classes = np.clip(predicted_classes, 0, len(np.unique(y_train))-1)
            prediction_probabilities = raw_predictions
            max_probs = np.abs(raw_predictions)
        
        # Calculate accuracy
        accuracy = np.mean(predicted_classes == y_valid)
        
        # Calculate confidence metrics
        avg_confidence = np.mean(max_probs)
        
        # Store results
        predictions_results[model_name] = {
            'predicted_classes': predicted_classes,
            'raw_predictions': raw_predictions,
            'probabilities': prediction_probabilities,
            'accuracy': accuracy,
            'avg_confidence': avg_confidence,
            'max_probs': max_probs
        }
        
        print(f"      ✅ Accuracy: {accuracy:.4f} ({accuracy*100:.2f}%)")
        print(f"      📊 Avg confidence: {avg_confidence:.4f}")
        print(f"      🎯 Sample predictions: {predicted_classes[:10]}")
        print(f"      🏷️  Actual labels:      {y_valid[:10]}")
        
    except Exception as e:
        print(f"      ❌ Error: {e}")

# STEP 4: Comprehensive model comparison
print("\n📊 COMPREHENSIVE MODEL COMPARISON")
print("="*70)

# Create detailed comparison DataFrame
comparison_data = {
    'sample_id': range(len(y_valid)),
    'actual': y_valid
}

# Add predictions from each model
for model_name, results in predictions_results.items():
    if 'predicted_classes' in results:
        comparison_data[f'{model_name}_pred'] = results['predicted_classes']
        comparison_data[f'{model_name}_correct'] = (results['predicted_classes'] == y_valid).astype(int)
        if 'max_probs' in results:
            comparison_data[f'{model_name}_confidence'] = results['max_probs']

comparison_df = pd.DataFrame(comparison_data)

# Print detailed accuracy summary
print("🎯 DETAILED ACCURACY SUMMARY:")
accuracies = []
for model_name, results in predictions_results.items():
    if 'accuracy' in results:
        acc = results['accuracy']
        conf = results.get('avg_confidence', 0)
        accuracies.append((model_name, acc, conf))
        print(f"   {model_name:15s}: {acc:.4f} ({acc*100:.2f}%) | Confidence: {conf:.4f}")

# Find best model
if accuracies:
    best_model = max(accuracies, key=lambda x: x[1])
    print(f"\n🏆 BEST MODEL: {best_model[0]}")
    print(f"   Accuracy: {best_model[1]:.4f} ({best_model[1]*100:.2f}%)")
    print(f"   Confidence: {best_model[2]:.4f}")

# STEP 5: Advanced analysis
print("\n📈 ADVANCED ANALYSIS:")

# Class distribution comparison
print("\n📊 Class distribution analysis:")
actual_dist = np.bincount(y_valid)
print(f"Actual distribution:     {actual_dist}")

for model_name, results in predictions_results.items():
    if 'predicted_classes' in results:
        pred_dist = np.bincount(results['predicted_classes'], minlength=len(actual_dist))
        print(f"{model_name:20s}: {pred_dist}")

# Per-class accuracy analysis
print("\n🎯 Per-class accuracy analysis:")
for model_name, results in predictions_results.items():
    if 'predicted_classes' in results:
        print(f"\n{model_name}:")
        y_pred = results['predicted_classes']
        
        class_accuracies = []
        for class_id in np.unique(y_valid):
            mask = y_valid == class_id
            if np.sum(mask) > 0:
                class_acc = np.mean(y_pred[mask] == class_id)
                count = np.sum(mask)
                class_accuracies.append(class_acc)
                print(f"  Class {class_id}: {class_acc:.4f} ({count:2d} samples)")
        
        # Overall class performance
        avg_class_acc = np.mean(class_accuracies)
        print(f"  Average class accuracy: {avg_class_acc:.4f}")

# Model agreement analysis
print("\n🤝 Model agreement analysis:")
if len(predictions_results) > 1:
    model_names = list(predictions_results.keys())
    agreements = []
    
    for i in range(len(model_names)):
        for j in range(i+1, len(model_names)):
            model1, model2 = model_names[i], model_names[j]
            if 'predicted_classes' in predictions_results[model1] and 'predicted_classes' in predictions_results[model2]:
                pred1 = predictions_results[model1]['predicted_classes']
                pred2 = predictions_results[model2]['predicted_classes']
                agreement = np.mean(pred1 == pred2)
                agreements.append((model1, model2, agreement))
                print(f"  {model1} vs {model2}: {agreement:.4f} agreement")
    
    if agreements:
        avg_agreement = np.mean([a[2] for a in agreements])
        print(f"  Average inter-model agreement: {avg_agreement:.4f}")

# STEP 6: Save comprehensive results
print("\n💾 Saving comprehensive results...")

timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

# Save detailed predictions
detailed_file = f"augmented_predictions_{timestamp}.csv"
comparison_df.to_csv(detailed_file, index=False)
print(f"   ✅ Detailed predictions: {detailed_file}")

# Save model performance summary
performance_file = f"model_performance_{timestamp}.csv"
performance_data = []

for model_name, results in predictions_results.items():
    if 'accuracy' in results:
        performance_data.append({
            'model': model_name,
            'accuracy': results['accuracy'],
            'avg_confidence': results.get('avg_confidence', 0),
            'parameters': models[model_name].count_params() if model_name in models else 0
        })

performance_df = pd.DataFrame(performance_data)
performance_df.to_csv(performance_file, index=False)
print(f"   ✅ Model performance: {performance_file}")

# Save comprehensive report
report_file = f"augmented_prediction_report_{timestamp}.txt"
with open(report_file, 'w') as f:
    f.write("AUGMENTED DATA PREDICTION REPORT\n")
    f.write("="*50 + "\n")
    f.write(f"Generated: {datetime.now()}\n\n")
    
    f.write("DATASET INFO:\n")
    f.write(f"Training samples: {len(y_train)} (augmented)\n")
    f.write(f"Validation samples: {len(y_valid)}\n")
    f.write(f"Number of classes: {len(np.unique(y_valid))}\n")
    f.write(f"Input shape: {X_valid.shape}\n")
    if 'data_dict' in locals():
        f.write(f"Augmentation factor: {data_dict['augmented_data'].shape[0] / data_dict['input_data'].shape[0]:.1f}x\n")
    f.write("\n")
    
    f.write("MODEL PERFORMANCE:\n")
    for model_name, acc, conf in accuracies:
        f.write(f"{model_name}: {acc:.4f} ({acc*100:.2f}%) | Confidence: {conf:.4f}\n")
    
    if accuracies:
        f.write(f"\nBEST MODEL: {best_model[0]} ({best_model[1]:.4f})\n")
    
    f.write(f"\nCLASS DISTRIBUTIONS:\n")
    f.write(f"Actual: {actual_dist}\n")
    for model_name, results in predictions_results.items():
        if 'predicted_classes' in results:
            pred_dist = np.bincount(results['predicted_classes'], minlength=len(actual_dist))
            f.write(f"{model_name}: {pred_dist}\n")

print(f"   ✅ Comprehensive report: {report_file}")

print("\n🎉 PREDICTION COMPLETE WITH AUGMENTED DATA!")
print("="*70)
print(f"📊 Processed {len(y_valid)} validation samples")
print(f"🤖 Used {len(models)} models on augmented dataset")
print(f"📈 Training data was augmented {data_dict['augmented_data'].shape[0] / data_dict['input_data'].shape[0]:.1f}x" if 'data_dict' in locals() else "")
print(f"📄 Files created:")
print(f"   - {detailed_file}")
print(f"   - {performance_file}")
print(f"   - {report_file}")

if accuracies:
    print(f"\n🏆 FINAL RESULTS (WITH AUGMENTATION):")
    for model_name, acc, conf in sorted(accuracies, key=lambda x: x[1], reverse=True):
        print(f"   {model_name:15s}: {acc:.4f} ({acc*100:.2f}%) | Confidence: {conf:.4f}")

print(f"\n📋 Available variables:")
print(f"   - models: Dictionary of loaded models")
print(f"   - predictions_results: All prediction results")
print(f"   - comparison_df: DataFrame with detailed comparisons")
print(f"   - X_train, X_valid, y_train, y_valid: Augmented dataset")
if 'data_dict' in locals():
    print(f"   - data_dict: Complete dataset with augmentation info")
